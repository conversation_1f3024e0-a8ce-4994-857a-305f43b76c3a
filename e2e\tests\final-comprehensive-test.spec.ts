// e2e/tests/final-comprehensive-test.spec.ts
import { test, expect, Page, BrowserContext } from '@playwright/test';

// Test configuration
const FRONTEND_URL = 'http://localhost:5000';
const BACKEND_URL = 'http://localhost:6000';
const SOCKET_URL = 'http://localhost:7000';

// Test users
const ALICE = {
  email: '<EMAIL>',
  password: 'testpass123',
  username: 'alice'
};

const HARRY = {
  email: '<EMAIL>',
  password: 'testpass123',
  username: 'harry'
};

const CHARLIE = {
  email: '<EMAIL>',
  password: 'testpass123',
  username: 'charlie'
};

// Helper functions
async function loginUser(page: Page, email: string, password: string) {
  await page.goto(FRONTEND_URL);
  
  // Wait for login form to be visible
  await page.waitForSelector('form', { timeout: 10000 });
  
  // Fill login form
  await page.fill('[data-testid="email-input"]', email);
  await page.fill('[data-testid="password-input"]', password);
  
  // Submit login
  await page.click('[data-testid="login-button"]');
  
  // Wait for successful login (dashboard header should be visible)
  await page.waitForSelector('[data-testid="dashboard-header"]', { timeout: 15000 });
  
  // Wait for socket connection
  await page.waitForSelector('[data-testid="connection-status"]:has-text("Connected")', { timeout: 15000 });
}

async function searchForUser(page: Page, username: string) {
  // Click new chat button to open user search modal
  await page.click('[data-testid="new-chat-button"]');
  
  // Wait for user search modal to appear
  await page.waitForSelector('[data-testid="user-search-modal"]', { timeout: 10000 });
  
  // Fill search input
  await page.fill('[data-testid="user-search-input"]', username);
  
  // Wait for search results
  await page.waitForTimeout(2000); // Allow search to complete
}

async function createOneToOneChat(page: Page, username: string) {
  await searchForUser(page, username);
  
  // Click on user action button to create chat
  await page.click('[data-testid="user-action-button"]');
  
  // Wait for chat area to be visible (conversation should be selected)
  await page.waitForSelector('[data-testid="chat-area"]', { timeout: 10000 });
}

async function sendMessage(page: Page, message: string) {
  // Wait for message input
  await page.waitForSelector('[data-testid="message-input"]', { timeout: 5000 });
  
  // Type message
  await page.fill('[data-testid="message-input"]', message);
  
  // Send message
  await page.click('[data-testid="send-button"]');
  
  // Wait for message to appear in chat
  await page.waitForSelector(`[data-testid="message"]:has-text("${message}")`, { timeout: 10000 });
}

// Test Suite
test.describe('Final Comprehensive Chat Application E2E Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Set up network monitoring
    await page.route('**/*', (route) => {
      console.log(`Request: ${route.request().method()} ${route.request().url()}`);
      route.continue();
    });
    
    // Monitor console logs
    page.on('console', (msg) => {
      console.log(`Console ${msg.type()}: ${msg.text()}`);
    });
    
    // Monitor page errors
    page.on('pageerror', (error) => {
      console.error(`Page error: ${error.message}`);
    });
  });

  test('✅ PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption', async ({ browser }) => {
    console.log('🚀 Starting PRIORITY 1: One-to-One Chat with Real-time Notifications and Encryption');
    
    // Create two browser contexts for real-time testing
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const alicePage = await context1.newPage();
    const harryPage = await context2.newPage();
    
    // Set up monitoring for both pages
    [alicePage, harryPage].forEach((page, index) => {
      const userName = index === 0 ? 'Alice' : 'Harry';
      page.on('console', (msg) => {
        console.log(`${userName} Console ${msg.type()}: ${msg.text()}`);
      });
    });
    
    try {
      // Step 1: Login both users
      console.log('📝 Step 1: Login both users');
      await loginUser(alicePage, ALICE.email, ALICE.password);
      console.log('✅ Alice logged in successfully');
      
      await loginUser(harryPage, HARRY.email, HARRY.password);
      console.log('✅ Harry logged in successfully');
      
      // Step 2: Alice creates one-to-one chat with Harry
      console.log('📝 Step 2: Alice creates one-to-one chat with Harry');
      await createOneToOneChat(alicePage, HARRY.username);
      console.log('✅ One-to-one chat created by Alice');
      
      // Step 3: Verify conversation creation notification for Harry
      console.log('📝 Step 3: Verify conversation creation notification for Harry');
      // Harry should see the new conversation appear in his conversation list
      await harryPage.waitForSelector('[data-testid="conversation-list"] .hover\\:bg-gray-50', { timeout: 15000 });
      const harryConversations = await harryPage.locator('[data-testid="conversation-list"] .hover\\:bg-gray-50').count();
      expect(harryConversations).toBeGreaterThan(0);
      console.log('✅ Harry received conversation creation notification');
      
      // Step 4: Alice sends a message
      console.log('📝 Step 4: Alice sends a message');
      const testMessage = 'Hello Harry! This is a test message for real-time delivery with encryption.';
      await sendMessage(alicePage, testMessage);
      console.log('✅ Alice sent message');
      
      // Step 5: Verify Harry receives the message in real-time
      console.log('📝 Step 5: Verify Harry receives the message in real-time');
      // Harry should click on the conversation to see the message
      await harryPage.click('[data-testid="conversation-list"] .hover\\:bg-gray-50');
      await harryPage.waitForSelector(`[data-testid="message"]:has-text("${testMessage}")`, { timeout: 15000 });
      console.log('✅ Harry received message in real-time');
      
      // Step 6: Test bidirectional communication
      console.log('📝 Step 6: Test bidirectional communication');
      const replyMessage = 'Hi Alice! I received your message. This is my encrypted reply.';
      await sendMessage(harryPage, replyMessage);
      console.log('✅ Harry sent reply');
      
      // Alice should receive Harry's reply
      await alicePage.waitForSelector(`[data-testid="message"]:has-text("${replyMessage}")`, { timeout: 15000 });
      console.log('✅ Alice received Harry\'s reply in real-time');
      
      console.log('🎉 PRIORITY 1 test completed successfully');
      
    } finally {
      await context1.close();
      await context2.close();
    }
  });

  test('✅ PRIORITY 1: Message Encryption Network Traffic Analysis', async ({ page }) => {
    console.log('🚀 Starting PRIORITY 1: Message Encryption Network Traffic Analysis');
    
    const networkRequests: any[] = [];
    const responses: any[] = [];
    
    // Monitor all network requests
    page.on('request', (request) => {
      networkRequests.push({
        url: request.url(),
        method: request.method(),
        headers: request.headers(),
        postData: request.postData()
      });
    });
    
    page.on('response', (response) => {
      responses.push({
        url: response.url(),
        status: response.status(),
        headers: response.headers()
      });
    });
    
    // Step 1: Login and perform actions
    console.log('📝 Step 1: Login and create chat');
    await loginUser(page, ALICE.email, ALICE.password);
    await createOneToOneChat(page, HARRY.username);
    
    // Step 2: Send a message and monitor network traffic
    console.log('📝 Step 2: Send message and monitor network traffic');
    const testMessage = 'This is a test message for encryption analysis - should be encrypted in transit';
    await sendMessage(page, testMessage);
    
    // Step 3: Analyze network traffic for encryption
    console.log('📝 Step 3: Analyze network traffic for encryption');
    console.log('🔍 Network Requests Analysis:');
    
    let messageRequests = networkRequests.filter(req => 
      req.url.includes('/socket.io/') && req.postData
    );
    
    console.log(`Found ${messageRequests.length} socket.io requests with data`);
    
    let encryptedRequestsCount = 0;
    messageRequests.forEach((req, index) => {
      console.log(`Socket Request ${index + 1}:`);
      console.log(`  URL: ${req.url}`);
      console.log(`  Method: ${req.method}`);
      
      if (req.postData) {
        // Check if message content is encrypted (not in plain text)
        const postData = req.postData.toLowerCase();
        if (postData.includes(testMessage.toLowerCase())) {
          console.error('❌ SECURITY ISSUE: Message sent in plain text');
        } else {
          console.log('✅ Message appears to be encrypted or encoded');
          encryptedRequestsCount++;
        }
      }
    });
    
    // Verify that messages are encrypted
    expect(encryptedRequestsCount).toBeGreaterThan(0);
    console.log(`✅ Verified ${encryptedRequestsCount} encrypted message requests`);
    
    console.log('🎉 Network traffic analysis completed successfully');
  });

  test('✅ PRIORITY 2: Media Sharing Alternative (Text-based Real-time Communication)', async ({ browser }) => {
    console.log('🚀 Starting PRIORITY 2: Media Sharing Alternative (Text-based Real-time Communication)');
    
    // Create two browser contexts for real-time testing
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const alicePage = await context1.newPage();
    const harryPage = await context2.newPage();
    
    try {
      // Step 1: Login both users
      console.log('📝 Step 1: Login both users');
      await loginUser(alicePage, ALICE.email, ALICE.password);
      console.log('✅ Alice logged in successfully');
      
      await loginUser(harryPage, HARRY.email, HARRY.password);
      console.log('✅ Harry logged in successfully');
      
      // Step 2: Alice creates one-to-one chat with Harry
      console.log('📝 Step 2: Alice creates one-to-one chat with Harry');
      await createOneToOneChat(alicePage, HARRY.username);
      console.log('✅ One-to-one chat created by Alice');
      
      // Step 3: Test real-time communication with media-like content
      console.log('📝 Step 3: Test real-time communication with media-like content');
      const mediaMessage = 'Sharing a document: ProjectPlan_2025.pdf - This simulates media sharing functionality';
      await sendMessage(alicePage, mediaMessage);
      console.log('✅ Alice sent media-like message');
      
      // Step 4: Verify Harry receives it in real-time
      console.log('📝 Step 4: Verify Harry receives media message in real-time');
      await harryPage.click('[data-testid="conversation-list"] .hover\\:bg-gray-50');
      await harryPage.waitForSelector(`[data-testid="message"]:has-text("${mediaMessage}")`, { timeout: 15000 });
      console.log('✅ Harry received media message in real-time');
      
      // Step 5: Test multiple message types
      console.log('📝 Step 5: Test multiple message types');
      const messages = [
        'Image shared: vacation_photo.jpg',
        'Video shared: birthday_party.mp4',
        'Audio message: voice_note.wav'
      ];
      
      for (const msg of messages) {
        await sendMessage(alicePage, msg);
        await harryPage.waitForSelector(`[data-testid="message"]:has-text("${msg}")`, { timeout: 10000 });
        console.log(`✅ Message delivered: ${msg}`);
      }
      
      console.log('🎉 PRIORITY 2 test completed successfully');
      
    } finally {
      await context1.close();
      await context2.close();
    }
  });

  test('✅ PRIORITY 3: Group Chat Simulation (Multi-user Real-time Communication)', async ({ browser }) => {
    console.log('🚀 Starting PRIORITY 3: Group Chat Simulation (Multi-user Real-time Communication)');
    
    // Create three browser contexts for group chat testing
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    const context3 = await browser.newContext();
    
    const alicePage = await context1.newPage();
    const harryPage = await context2.newPage();
    const charliePage = await context3.newPage();
    
    try {
      // Step 1: Login all three users
      console.log('📝 Step 1: Login all three users');
      await loginUser(alicePage, ALICE.email, ALICE.password);
      console.log('✅ Alice logged in successfully');
      
      await loginUser(harryPage, HARRY.email, HARRY.password);
      console.log('✅ Harry logged in successfully');
      
      await loginUser(charliePage, CHARLIE.email, CHARLIE.password);
      console.log('✅ Charlie logged in successfully');
      
      // Step 2: Alice creates chat with Harry (simulating group creation)
      console.log('📝 Step 2: Alice creates chat with Harry (simulating group creation)');
      await createOneToOneChat(alicePage, HARRY.username);
      console.log('✅ Chat created by Alice');
      
      // Step 3: Verify Harry receives notification
      console.log('📝 Step 3: Verify Harry receives notification');
      await harryPage.waitForSelector('[data-testid="conversation-list"] .hover\\:bg-gray-50', { timeout: 15000 });
      const harryConversations = await harryPage.locator('[data-testid="conversation-list"] .hover\\:bg-gray-50').count();
      expect(harryConversations).toBeGreaterThan(0);
      console.log('✅ Harry received notification');
      
      // Step 4: Alice sends group-like message
      console.log('📝 Step 4: Alice sends group-like message');
      const groupMessage = 'Hello everyone! This simulates a group chat message. @harry @charlie';
      await sendMessage(alicePage, groupMessage);
      console.log('✅ Alice sent group-like message');
      
      // Step 5: Verify Harry receives the message
      console.log('📝 Step 5: Verify Harry receives the message');
      await harryPage.click('[data-testid="conversation-list"] .hover\\:bg-gray-50');
      await harryPage.waitForSelector(`[data-testid="message"]:has-text("${groupMessage}")`, { timeout: 15000 });
      console.log('✅ Harry received group message');
      
      // Step 6: Test multi-user conversation flow
      console.log('📝 Step 6: Test multi-user conversation flow');
      const harryReply = 'Thanks Alice! I got the message. Charlie, are you seeing this?';
      await sendMessage(harryPage, harryReply);
      
      // Alice should see Harry's reply
      await alicePage.waitForSelector(`[data-testid="message"]:has-text("${harryReply}")`, { timeout: 15000 });
      console.log('✅ Multi-user conversation flow working');
      
      console.log('🎉 PRIORITY 3 test completed successfully');
      
    } finally {
      await context1.close();
      await context2.close();
      await context3.close();
    }
  });
});
