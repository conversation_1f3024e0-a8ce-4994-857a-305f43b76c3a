// frontend/src/components/calling/VideoContainer.tsx
import React, { useRef, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { User, VideoOff } from 'lucide-react';
import { selectCallingState, selectCurrentCall } from '../../store/slices/callingSlice';


interface VideoContainerProps {
  isMinimized?: boolean;
}


export const VideoContainer: React.FC<VideoContainerProps> = ({ isMinimized = false }) => {
  const localVideoRef = useRef<HTMLVideoElement>(null);
  const remoteVideoRef = useRef<HTMLVideoElement>(null);
  const localAudioRef = useRef<HTMLAudioElement>(null);
  const remoteAudioRef = useRef<HTMLAudioElement>(null);

  const { localStream, remoteStream, controls } = useSelector(selectCallingState);
  const currentCall = useSelector(selectCurrentCall);


  // Set up local video stream
  useEffect(() => {
    if (localStream && localVideoRef.current) {
      console.log('🎥 Setting local video stream');
      localVideoRef.current.srcObject = localStream;
    }
  }, [localStream]);


  // Set up remote video stream
  useEffect(() => {
    if (remoteStream && remoteVideoRef.current) {
      console.log('🎥 Setting remote video stream');
      remoteVideoRef.current.srcObject = remoteStream;
    }
  }, [remoteStream]);


  // Set up local audio stream for audio calls
  useEffect(() => {
    if (localStream && localAudioRef.current && currentCall.type === 'audio') {
      console.log('🎵 Setting local audio stream');
      localAudioRef.current.srcObject = localStream;
      localAudioRef.current.play().catch(console.error);
    }
  }, [localStream, currentCall.type]);


  // Set up remote audio stream for audio calls
  useEffect(() => {
    if (remoteStream && remoteAudioRef.current && currentCall.type === 'audio') {
      console.log('🎵 Setting remote audio stream');
      remoteAudioRef.current.srcObject = remoteStream;
      remoteAudioRef.current.play().catch(console.error);
    }
  }, [remoteStream, currentCall.type]);


  // Audio-only call
  if (currentCall.type === 'audio') {
    return (
      <div className={`flex items-center justify-center bg-gray-800 ${isMinimized ? 'h-32' : 'h-full'
        }`}>
        {/* Hidden audio elements for audio playback */}
        <audio
          ref={localAudioRef}
          autoPlay
          muted={controls.isMuted}
          style={{ display: 'none' }}
        />
        <audio
          ref={remoteAudioRef}
          autoPlay
          muted={false}
          style={{ display: 'none' }}
        />

        <div className="text-center">
          <div className={`rounded-full bg-gray-600 flex items-center justify-center mx-auto mb-4 ${isMinimized ? 'w-16 h-16' : 'w-32 h-32'
            }`}>
            <User className={`text-gray-300 ${isMinimized ? 'w-8 h-8' : 'w-16 h-16'}`} />
          </div>
          <p className={`text-gray-300 ${isMinimized ? 'text-sm' : 'text-lg'}`}>
            Audio Call
          </p>
          {currentCall.participants.length > 0 && (
            <p className={`text-gray-400 ${isMinimized ? 'text-xs' : 'text-sm'}`}>
              {currentCall.participants[0].firstName} {currentCall.participants[0].lastName}
            </p>
          )}
        </div>
      </div>
    );
  }


  // Minimized video view
  if (isMinimized) {
    return (
      <div className="relative h-32 bg-gray-900 rounded overflow-hidden">
        {/* Remote video (main) */}
        <video
          ref={remoteVideoRef}
          className="w-full h-full object-cover"
          autoPlay
          playsInline
          muted={false}
        />

        {/* Local video (small overlay) */}
        <div className="absolute top-2 right-2 w-16 h-12 bg-gray-800 rounded overflow-hidden border border-gray-600">
          {controls.isVideoMuted ? (
            <div className="w-full h-full flex items-center justify-center bg-gray-700">
              <VideoOff className="w-4 h-4 text-gray-400" />
            </div>
          ) : (
            <video
              ref={localVideoRef}
              className="w-full h-full object-cover"
              autoPlay
              playsInline
              muted
            />
          )}
        </div>


        {/* No remote video placeholder */}
        {!remoteStream && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-800">
            <div className="text-center">
              <User className="w-8 h-8 text-gray-400 mx-auto mb-1" />
              <p className="text-gray-400 text-xs">Connecting...</p>
            </div>
          </div>
        )}
      </div>
    );
  }


  // Full screen video view
  return (
    <div className="relative w-full h-full bg-gray-900">
      {/* Remote video (main) */}
      <video
        ref={remoteVideoRef}
        className="w-full h-full object-cover"
        autoPlay
        playsInline
        muted={false}
      />


      {/* Local video (overlay) */}
      <div className="absolute top-4 right-4 w-48 h-36 bg-gray-800 rounded-lg overflow-hidden border-2 border-gray-600 shadow-lg">
        {controls.isVideoMuted ? (
          <div className="w-full h-full flex items-center justify-center bg-gray-700">
            <div className="text-center">
              <VideoOff className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-400 text-sm">Camera off</p>
            </div>
          </div>
        ) : (
          <video
            ref={localVideoRef}
            className="w-full h-full object-cover"
            autoPlay
            playsInline
            muted
          />
        )}
      </div>


      {/* No remote video placeholder */}
      {!remoteStream && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-800">
          <div className="text-center">
            <div className="w-32 h-32 rounded-full bg-gray-600 flex items-center justify-center mx-auto mb-4">
              <User className="w-16 h-16 text-gray-300" />
            </div>
            <p className="text-gray-300 text-xl mb-2">
              {currentCall.participants.length > 0
                ? `${currentCall.participants[0].firstName} ${currentCall.participants[0].lastName}`
                : 'Connecting...'
              }
            </p>
            <p className="text-gray-400">Waiting for video...</p>
          </div>
        </div>
      )}


      {/* Connection status overlay */}
      {currentCall.status !== 'active' && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
            <p className="text-white text-lg">
              {currentCall.status === 'initiating' && 'Calling...'}
              {currentCall.status === 'ringing' && 'Ringing...'}
              {currentCall.status === 'ending' && 'Ending call...'}
            </p>
          </div>
        </div>
      )}


      {/* Mobile responsive adjustments */}
      <style>{`
        @media (max-width: 768px) {
          .absolute.top-4.right-4 {
            width: 120px;
            height: 90px;
            top: 10px;
            right: 10px;
          }
        }
      `}</style>
    </div>
  );
};


export default VideoContainer;
