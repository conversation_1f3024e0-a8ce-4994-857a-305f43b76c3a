// frontend/src/services/webrtc/WebRTCService.ts
import { store } from '../../store';
import { setRemoteStream, updateCallStatus, setCallingError, startCallTimer } from '../../store/slices/callingSlice';
import { audioTestingService } from './AudioTestingService';
import { MediaService } from './MediaService';

export class WebRTCService {
  private peerConnection: RTCPeerConnection | null = null;
  private localStream: MediaStream | null = null;
  private remoteStream: MediaStream | null = null;
  private currentCallId: string | null = null;
  private signalingService: any = null; // Will be injected
  private mediaService: MediaService = new MediaService();

  // Exact configuration from HTML test
  private rtcConfig: RTCConfiguration = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' }
    ]
  };

  constructor() {
    console.log('🔧 WebRTCService initialized');
  }

  setSignalingService(signalingService: any) {
    this.signalingService = signalingService;
  }

  setCurrentCallId(callId: string) {
    this.currentCallId = callId;
  }

  async initializePeerConnection(): Promise<RTCPeerConnection> {
    console.log('🔧 Starting peer connection initialization');
    
    if (this.peerConnection) {
      console.log('🔄 Existing peer connection found, closing it first');
      this.closePeerConnection();
    }

    try {
      console.log('🔧 Initializing peer connection with config:', this.rtcConfig);
      this.peerConnection = new RTCPeerConnection(this.rtcConfig);
      
      if (!this.peerConnection) {
        throw new Error('Failed to create RTCPeerConnection');
      }
      
      console.log('✅ Peer connection created successfully');
      console.log('🔍 Initial connection state:', this.peerConnection.connectionState);
      
      this.setupPeerConnectionEventHandlers();
      
      return this.peerConnection;
    } catch (error) {
      console.error('❌ Failed to initialize peer connection:', error);
      this.peerConnection = null;
      throw error;
    }
  }

  private setupPeerConnectionEventHandlers() {
    if (!this.peerConnection) return;

    // Handle remote stream (exact HTML test pattern)
    this.peerConnection.ontrack = (event) => {
      console.log('🎥 Received remote stream');
      const stream = event.streams[0];
      this.remoteStream = stream;

      // Dispatch to Redux store
      store.dispatch(setRemoteStream(stream));

      // Enhanced audio debugging - Log track information (matching HTML test)
      const audioTracks = stream.getAudioTracks();
      const videoTracks = stream.getVideoTracks();
      console.log(`📊 Remote stream has ${audioTracks.length} audio tracks and ${videoTracks.length} video tracks`);

      // Debug audio tracks in detail
      audioTracks.forEach((track, index) => {
        console.log(`🎤 Remote audio track ${index}:`, {
          id: track.id,
          label: track.label,
          enabled: track.enabled,
          muted: track.muted,
          readyState: track.readyState,
          kind: track.kind
        });

        // Listen for track events
        track.onended = () => console.log(`🎤 Remote audio track ${index} ended`);
        track.onmute = () => console.log(`🎤 Remote audio track ${index} muted`);
        track.onunmute = () => console.log(`🎤 Remote audio track ${index} unmuted`);
      });

      // Debug video tracks in detail
      videoTracks.forEach((track, index) => {
        console.log(`📹 Remote video track ${index}:`, {
          id: track.id,
          label: track.label,
          enabled: track.enabled,
          muted: track.muted,
          readyState: track.readyState,
          kind: track.kind
        });
      });

      // Test audio playback capability
      this.testRemoteAudioPlayback(stream);
    };

    // Handle ICE candidates (exact HTML test pattern)
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        console.log('🧊 Sending ICE candidate');
        if (this.signalingService && this.currentCallId) {
          this.signalingService.emitICECandidate({
            callId: this.currentCallId,
            candidate: event.candidate
          });
        }
      } else {
        console.log('🧊 ICE gathering complete');
      }
    };

    // Handle connection state changes (exact HTML test pattern)
    this.peerConnection.onconnectionstatechange = () => {
      if (!this.peerConnection) return;
      
      const state = this.peerConnection.connectionState;
      console.log(`🔗 Connection state: ${state}`);

      switch (state) {
        case 'connected':
          console.log('✅ WebRTC connection established successfully!');
          store.dispatch(updateCallStatus({ status: 'active' }));
          store.dispatch(startCallTimer());
          break;
        case 'failed':
          console.error('❌ WebRTC connection failed');
          store.dispatch(setCallingError('WebRTC connection failed'));
          break;
        case 'disconnected':
          console.log('⚠️ WebRTC connection disconnected');
          break;
        case 'closed':
          console.log('🔒 WebRTC connection closed');
          break;
      }
    };

    // Handle ICE connection state changes (HTML test pattern)
    this.peerConnection.oniceconnectionstatechange = () => {
      if (!this.peerConnection) return;
      console.log(`🧊 ICE connection state: ${this.peerConnection.iceConnectionState}`);
    };

    // Handle signaling state changes
    this.peerConnection.onsignalingstatechange = () => {
      if (!this.peerConnection) return;
      console.log(`📡 Signaling state: ${this.peerConnection.signalingState}`);
    };
  }

  async createOffer(): Promise<RTCSessionDescriptionInit> {
    if (!this.peerConnection) {
      throw new Error('No peer connection available');
    }

    console.log('📤 Creating WebRTC offer');

    // Log current senders before creating offer
    const senders = this.peerConnection.getSenders();
    console.log(`📊 Current senders count: ${senders.length}`);
    senders.forEach((sender, index) => {
      const track = sender.track;
      if (track) {
        console.log(`📤 Sender ${index}: ${track.kind} track (${track.id})`);
        if (track.kind === 'audio') {
          console.log(`🎤 Audio track enabled: ${track.enabled}, muted: ${track.muted}`);
        }
      } else {
        console.log(`📤 Sender ${index}: no track`);
      }
    });

    // Create offer with exact constraints from HTML test
    const offer = await this.peerConnection.createOffer({
      offerToReceiveAudio: true,
      offerToReceiveVideo: true
    });

    console.log('📤 Offer created:', {
      type: offer.type,
      sdpLength: offer.sdp?.length || 0,
      hasAudio: offer.sdp?.includes('m=audio') || false,
      hasVideo: offer.sdp?.includes('m=video') || false
    });

    await this.peerConnection.setLocalDescription(offer);
    console.log('📤 Local description set (offer)');

    // Emit the offer via signaling service
    if (this.signalingService && this.currentCallId) {
      console.log('📤 Emitting WebRTC offer via signaling service');
      this.signalingService.emitWebRTCOffer({
        callId: this.currentCallId,
        offer: offer
      });
    } else {
      console.error('❌ Cannot emit offer: missing signaling service or call ID');
    }

    return offer;
  }

  async createAnswer(): Promise<RTCSessionDescriptionInit> {
    if (!this.peerConnection) {
      throw new Error('No peer connection available');
    }

    console.log('📥 Creating WebRTC answer');

    // Log current senders before creating answer
    const senders = this.peerConnection.getSenders();
    console.log(`📊 Current senders count: ${senders.length}`);
    senders.forEach((sender, index) => {
      const track = sender.track;
      if (track) {
        console.log(`📥 Sender ${index}: ${track.kind} track (${track.id})`);
        if (track.kind === 'audio') {
          console.log(`🎤 Audio track enabled: ${track.enabled}, muted: ${track.muted}`);
        }
      }
    });

    const answer = await this.peerConnection.createAnswer();

    console.log('📥 Answer created:', {
      type: answer.type,
      sdpLength: answer.sdp?.length || 0,
      hasAudio: answer.sdp?.includes('m=audio') || false,
      hasVideo: answer.sdp?.includes('m=video') || false
    });

    await this.peerConnection.setLocalDescription(answer);
    console.log('📥 Local description set (answer)');

    // Emit the answer via signaling service
    if (this.signalingService && this.currentCallId) {
      console.log('📥 Emitting WebRTC answer via signaling service');
      this.signalingService.emitWebRTCAnswer({
        callId: this.currentCallId,
        answer: answer
      });
    } else {
      console.error('❌ Cannot emit answer: missing signaling service or call ID');
    }

    return answer;
  }

  async handleRemoteOffer(offer: RTCSessionDescriptionInit): Promise<void> {
    if (!this.peerConnection) {
      await this.initializePeerConnection();
    }

    if (!this.peerConnection) {
      throw new Error('Failed to initialize peer connection');
    }

    console.log('📤 Handling remote offer');
    await this.peerConnection.setRemoteDescription(new RTCSessionDescription(offer));
    console.log('📤 Remote description set (offer)');
  }

  async handleRemoteAnswer(answer: RTCSessionDescriptionInit): Promise<void> {
    if (!this.peerConnection) {
      throw new Error('No peer connection available');
    }

    console.log('📥 Handling remote answer');
    await this.peerConnection.setRemoteDescription(new RTCSessionDescription(answer));
    console.log('📥 Remote description set (answer)');
  }

  async handleICECandidate(candidate: RTCIceCandidateInit): Promise<void> {
    if (!this.peerConnection) {
      console.warn('⚠️ No peer connection available for ICE candidate');
      return;
    }

    try {
      console.log('🧊 Adding ICE candidate');
      await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
      console.log('🧊 ICE candidate added successfully');
    } catch (error) {
      console.error('❌ Failed to add ICE candidate:', error);
    }
  }

  async addLocalStream(stream: MediaStream): Promise<void> {
    console.log('🎥 Attempting to add local stream to peer connection');
    console.log('🔍 Peer connection state:', this.peerConnection ? this.peerConnection.connectionState : 'null');

    if (!this.peerConnection) {
      console.error('❌ No peer connection available when trying to add local stream');
      throw new Error('No peer connection available');
    }

    if (!stream) {
      console.error('❌ No stream provided to addLocalStream');
      throw new Error('No stream provided');
    }

    console.log('🎥 Adding local stream to peer connection');
    this.localStream = stream;

    // Enhanced audio debugging - Log detailed track information
    const audioTracks = stream.getAudioTracks();
    const videoTracks = stream.getVideoTracks();
    console.log(`📊 Local stream has ${audioTracks.length} audio tracks and ${videoTracks.length} video tracks`);

    // Debug audio tracks in detail
    audioTracks.forEach((track, index) => {
      console.log(`🎤 Local audio track ${index}:`, {
        id: track.id,
        label: track.label,
        enabled: track.enabled,
        muted: track.muted,
        readyState: track.readyState,
        kind: track.kind,
        constraints: track.getConstraints(),
        settings: track.getSettings()
      });
    });

    // Debug video tracks in detail
    videoTracks.forEach((track, index) => {
      console.log(`📹 Local video track ${index}:`, {
        id: track.id,
        label: track.label,
        enabled: track.enabled,
        muted: track.muted,
        readyState: track.readyState,
        kind: track.kind,
        constraints: track.getConstraints(),
        settings: track.getSettings()
      });
    });

    // Add tracks to peer connection with enhanced logging
    stream.getTracks().forEach((track, index) => {
      if (this.peerConnection && this.localStream) {
        console.log(`🎵 Adding ${track.kind} track ${index} to peer connection`);
        const sender = this.peerConnection.addTrack(track, this.localStream);
        console.log(`✅ Track ${track.kind} ${index} added successfully, sender:`, sender);

        // Log sender parameters for audio debugging (getParameters is synchronous)
        if (track.kind === 'audio') {
          try {
            const params = sender.getParameters();
            console.log(`🎤 Audio sender parameters:`, params);
          } catch (err) {
            console.error('❌ Failed to get audio sender parameters:', err);
          }
        }
      }
    });

    // Test local audio playback capability
    await this.testLocalAudioCapture(stream);
  }

  toggleAudioTrack(): boolean {
    if (!this.localStream) {
      console.warn('⚠️ No local stream available for audio toggle');
      return false;
    }

    const audioTracks = this.localStream.getAudioTracks();
    if (audioTracks.length === 0) {
      console.warn('⚠️ No audio tracks found');
      return false;
    }

    const audioTrack = audioTracks[0];
    audioTrack.enabled = !audioTrack.enabled;
    const isAudioMuted = !audioTrack.enabled;
    
    console.log(`🎤 Audio ${isAudioMuted ? 'muted' : 'unmuted'}`);
    return isAudioMuted;
  }

  toggleVideoTrack(): boolean {
    if (!this.localStream) {
      console.warn('⚠️ No local stream available for video toggle');
      return false;
    }

    const videoTracks = this.localStream.getVideoTracks();
    if (videoTracks.length === 0) {
      console.warn('⚠️ No video tracks found');
      return false;
    }

    const videoTrack = videoTracks[0];
    videoTrack.enabled = !videoTrack.enabled;
    const isVideoMuted = !videoTrack.enabled;
    
    console.log(`📹 Video ${isVideoMuted ? 'muted' : 'unmuted'}`);
    return isVideoMuted;
  }

  async startScreenShare(): Promise<MediaStream | null> {
    try {
      console.log('🖥️ Starting screen share');
      
      const screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true
      });

      if (this.peerConnection && this.localStream) {
        // Replace video track with screen share
        const videoTrack = screenStream.getVideoTracks()[0];
        const sender = this.peerConnection.getSenders().find(s => 
          s.track && s.track.kind === 'video'
        );

        if (sender) {
          await sender.replaceTrack(videoTrack);
          console.log('🖥️ Screen share track replaced');
        }
      }

      return screenStream;
    } catch (error) {
      console.error('❌ Failed to start screen share:', error);
      return null;
    }
  }

  stopScreenShare(): void {
    if (!this.localStream || !this.peerConnection) {
      return;
    }

    console.log('🖥️ Stopping screen share');
    
    // Replace screen share track with camera track
    const videoTrack = this.localStream.getVideoTracks()[0];
    const sender = this.peerConnection.getSenders().find(s => 
      s.track && s.track.kind === 'video'
    );

    if (sender && videoTrack) {
      sender.replaceTrack(videoTrack);
      console.log('🖥️ Screen share stopped, camera restored');
    }
  }

  /**
   * Handle incoming WebRTC offer (exact HTML test pattern)
   */
  async handleWebRTCOffer(data: { callId: string; offer: RTCSessionDescriptionInit }): Promise<void> {
    try {
      console.log('📥 Processing incoming WebRTC offer...');
      
      // Setup peer connection if not already done
      if (!this.peerConnection) {
        console.log('📥 Setting up peer connection for incoming offer');
        await this.initializePeerConnection();
      }
      
      // Add local stream if available (check if not already added)
      if (this.localStream) {
        const existingSenders = this.peerConnection!.getSenders();
        this.localStream.getTracks().forEach(track => {
          const existingSender = existingSenders.find(sender => sender.track === track);
          if (!existingSender) {
            this.peerConnection!.addTrack(track, this.localStream!);
            console.log(`📥 Added ${track.kind} track to peer connection (enabled: ${track.enabled}, muted: ${track.muted})`);
          } else {
            console.log(`📥 ${track.kind} track already exists in peer connection`);
          }
        });
        
        // Log current senders for debugging
        const senders = this.peerConnection!.getSenders();
        console.log(`📊 Peer connection has ${senders.length} senders`);
        senders.forEach((sender, index) => {
          if (sender.track) {
            console.log(`📊 Sender ${index}: ${sender.track.kind} track (enabled: ${sender.track.enabled})`);
          }
        });
      } else {
        console.log('⚠️ No local stream available when processing offer');
      }

      console.log('📥 Setting remote description...');
      await this.peerConnection!.setRemoteDescription(new RTCSessionDescription(data.offer));
      console.log('📥 Remote description set successfully');
      
      // Automatically create and send answer
      console.log('📥 Creating WebRTC answer...');
      const answer = await this.peerConnection!.createAnswer();
      
      console.log('📥 Setting local description with answer...');
      await this.peerConnection!.setLocalDescription(answer);
      
      console.log(`📥 Sending WebRTC answer for call ${data.callId}`);
      
      if (this.signalingService && data.callId) {
        this.signalingService.emitWebRTCAnswer({
          callId: data.callId,
          answer: answer
        });
        console.log('✅ WebRTC answer sent successfully');
      } else {
        console.log('❌ Cannot send answer: missing signaling service or callId', 'error');
      }
      
    } catch (error) {
      console.error('❌ Failed to handle WebRTC offer:', error);
      store.dispatch(setCallingError(`Failed to handle WebRTC offer: ${error instanceof Error ? error.message : 'Unknown error'}`));
    }
  }

  /**
   * Handle incoming WebRTC answer (exact HTML test pattern)
   */
  async handleWebRTCAnswer(data: { callId: string; answer: RTCSessionDescriptionInit }): Promise<void> {
    try {
      console.log('📥 Processing WebRTC answer...');
      if (this.peerConnection) {
        await this.peerConnection.setRemoteDescription(new RTCSessionDescription(data.answer));
        console.log('✅ WebRTC answer processed successfully');
      } else {
        console.log('❌ No peer connection available for answer');
      }
    } catch (error) {
      console.error('❌ Failed to handle WebRTC answer:', error);
      store.dispatch(setCallingError(`Failed to handle WebRTC answer: ${error instanceof Error ? error.message : 'Unknown error'}`));
    }
  }

  /**
   * Handle incoming ICE candidate (exact HTML test pattern)
   */
  async handleICECandidate(data: { callId: string; candidate: RTCIceCandidateInit }): Promise<void> {
    try {
      console.log('🧊 Processing ICE candidate...');
      if (this.peerConnection) {
        await this.peerConnection.addIceCandidate(new RTCIceCandidate(data.candidate));
        console.log('✅ ICE candidate added successfully');
      } else {
        console.log('❌ No peer connection available for ICE candidate');
      }
    } catch (error) {
      console.error('❌ Failed to handle ICE candidate:', error);
      // ICE candidate errors are not critical, don't dispatch error to store
    }
  }

  /**
   * Create WebRTC offer (exact HTML test pattern)
   */
  async createOffer(): Promise<void> {
    if (!this.localStream) {
      console.error('❌ No local stream available');
      return;
    }

    try {
      console.log('📤 Starting WebRTC offer creation...');
      
      if (!this.peerConnection) {
        await this.initializePeerConnection();
      }
      
      // Add local stream to peer connection (check if not already added)
      const existingSenders = this.peerConnection!.getSenders();
      this.localStream.getTracks().forEach(track => {
        const existingSender = existingSenders.find(sender => sender.track === track);
        if (!existingSender) {
          this.peerConnection!.addTrack(track, this.localStream!);
          console.log(`📤 Added ${track.kind} track to peer connection (enabled: ${track.enabled}, muted: ${track.muted})`);
        } else {
          console.log(`📤 ${track.kind} track already exists in peer connection`);
        }
      });
      
      // Log current senders for debugging
      const senders = this.peerConnection!.getSenders();
      console.log(`📊 Peer connection has ${senders.length} senders`);
      senders.forEach((sender, index) => {
        if (sender.track) {
          console.log(`📊 Sender ${index}: ${sender.track.kind} track (enabled: ${sender.track.enabled})`);
        }
      });

      console.log('📤 Creating offer with constraints...');
      const offer = await this.peerConnection!.createOffer({
        offerToReceiveAudio: true,
        offerToReceiveVideo: true
      });
      
      console.log('📤 Setting local description...');
      await this.peerConnection!.setLocalDescription(offer);

      console.log(`📤 Sending WebRTC offer for call ${this.currentCallId}`);
      
      if (this.currentCallId && this.signalingService) {
        this.signalingService.emitWebRTCOffer({
          callId: this.currentCallId,
          offer: offer
        });
        console.log('✅ WebRTC offer sent successfully');
      } else {
        console.log('❌ Cannot send offer: missing callId or signaling service');
      }
      
    } catch (error) {
      console.error('❌ Failed to create offer:', error);
      store.dispatch(setCallingError(`Failed to create offer: ${error instanceof Error ? error.message : 'Unknown error'}`));
    }
  }

  /**
   * Close peer connection and cleanup with comprehensive media cleanup
   */
  closePeerConnection(): void {
    console.log('🔒 Starting comprehensive WebRTC cleanup...');

    try {
      // Close peer connection with detailed logging
      if (this.peerConnection) {
        console.log(`🔒 Closing peer connection (state: ${this.peerConnection.connectionState})`);

        // Get all senders and receivers before closing
        const senders = this.peerConnection.getSenders();
        const receivers = this.peerConnection.getReceivers();

        console.log(`📊 Cleaning up ${senders.length} senders and ${receivers.length} receivers`);

        // Stop all sender tracks
        senders.forEach((sender, index) => {
          if (sender.track) {
            console.log(`🛑 Stopping sender ${index} track: ${sender.track.kind} (${sender.track.label})`);
            sender.track.stop();
          }
        });

        // Stop all receiver tracks
        receivers.forEach((receiver, index) => {
          if (receiver.track) {
            console.log(`🛑 Stopping receiver ${index} track: ${receiver.track.kind} (${receiver.track.label})`);
            receiver.track.stop();
          }
        });

        this.peerConnection.close();
        this.peerConnection = null;
        console.log('✅ Peer connection closed');
      } else {
        console.log('ℹ️ No peer connection to close');
      }

      // Stop local stream tracks with detailed logging
      if (this.localStream) {
        console.log('🛑 Stopping local stream tracks...');
        this.localStream.getTracks().forEach((track, index) => {
          console.log(`🛑 Stopping local ${track.kind} track ${index}: ${track.label} (readyState: ${track.readyState})`);
          track.stop();
        });
        this.localStream = null;
        console.log('✅ Local stream cleaned up');
      } else {
        console.log('ℹ️ No local stream to cleanup');
      }

      // Stop remote stream tracks with detailed logging
      if (this.remoteStream) {
        console.log('🛑 Stopping remote stream tracks...');
        this.remoteStream.getTracks().forEach((track, index) => {
          console.log(`🛑 Stopping remote ${track.kind} track ${index}: ${track.label} (readyState: ${track.readyState})`);
          track.stop();
        });
        this.remoteStream = null;
        console.log('✅ Remote stream cleaned up');
      } else {
        console.log('ℹ️ No remote stream to cleanup');
      }

      // Clear call ID
      this.currentCallId = null;

      console.log('✅ WebRTC cleanup completed successfully');
    } catch (error) {
      console.error('❌ Error during WebRTC cleanup:', error);

      // Force cleanup even if errors occurred
      this.peerConnection = null;
      this.localStream = null;
      this.remoteStream = null;
      this.currentCallId = null;

      console.log('⚠️ Forced WebRTC cleanup completed after error');
    }
  }

  // Getters
  getPeerConnection(): RTCPeerConnection | null {
    return this.peerConnection;
  }

  getLocalStream(): MediaStream | null {
    return this.localStream;
  }

  getRemoteStream(): MediaStream | null {
    return this.remoteStream;
  }

  getConnectionState(): RTCPeerConnectionState | null {
    return this.peerConnection?.connectionState || null;
  }

  /**
   * Enhanced local audio capture testing with fake device detection
   */
  private async testLocalAudioCapture(stream: MediaStream): Promise<void> {
    const audioTracks = stream.getAudioTracks();
    if (audioTracks.length === 0) {
      console.warn('⚠️ No audio tracks in local stream for testing');
      return;
    }

    console.log('🧪 Testing local audio capture with enhanced diagnostics...');

    try {
      // Use enhanced audio testing service
      const testResult = await audioTestingService.testAudioCapabilities();
      
      console.log('📊 Enhanced Audio Test Results:');
      console.log(`   Device: ${testResult.deviceLabel}`);
      console.log(`   Real Device: ${testResult.hasRealAudioDevice ? '✅' : '❌'}`);
      console.log(`   Max Audio Level: ${testResult.audioLevel.toFixed(2)}%`);
      console.log(`   Test Duration: ${testResult.testDuration}ms`);
      
      if (testResult.isFakeDevice) {
        console.error('🚨 CRITICAL: Fake audio device detected!');
        console.error('   This explains why audio transmission is not working.');
        console.error('   Solution: Remove --use-fake-device-for-media-stream from browser flags.');
        
        // Dispatch error to Redux store
        store.dispatch(setCallingError(
          'Fake audio device detected. Please use real microphone for testing.'
        ));
      } else if (testResult.audioLevel < 0.1) {
        console.warn('⚠️ Very low audio levels detected.');
        console.warn('   Please check microphone settings and speak into the microphone.');
      } else {
        console.log('✅ Real audio device with good signal levels detected!');
      }
      
    } catch (error) {
      console.error('❌ Enhanced audio testing failed:', error);
      // Fallback to basic testing
      this.basicAudioLevelTest(stream);
    }
  }

  /**
   * Fallback basic audio level testing
   */
  private basicAudioLevelTest(stream: MediaStream): void {
    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const source = audioContext.createMediaStreamSource(stream);
      const analyser = audioContext.createAnalyser();

      source.connect(analyser);
      analyser.fftSize = 256;

      const bufferLength = analyser.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);

      let testCount = 0;
      const testInterval = setInterval(() => {
        analyser.getByteFrequencyData(dataArray);
        const average = dataArray.reduce((a, b) => a + b) / bufferLength;

        console.log(`🎤 Basic audio level test ${testCount + 1}: ${average.toFixed(2)}`);

        testCount++;
        if (testCount >= 5) {
          clearInterval(testInterval);
          audioContext.close();
          console.log('✅ Basic audio capture test completed');
        }
      }, 1000);

    } catch (error) {
      console.error('❌ Failed to perform basic audio test:', error);
    }
  }

  /**
   * Test remote audio playback capability
   */
  private testRemoteAudioPlayback(stream: MediaStream): void {
    const audioTracks = stream.getAudioTracks();
    if (audioTracks.length === 0) {
      console.warn('⚠️ No audio tracks in remote stream for testing');
      return;
    }

    console.log('🧪 Testing remote audio playback...');

    // Create audio element for testing
    try {
      const audioElement = document.createElement('audio');
      audioElement.srcObject = stream;
      audioElement.autoplay = true;
      audioElement.muted = false; // Ensure not muted for testing

      audioElement.onloadedmetadata = () => {
        console.log('✅ Remote audio metadata loaded');
      };

      audioElement.oncanplay = () => {
        console.log('✅ Remote audio can play');
      };

      audioElement.onerror = (error) => {
        console.error('❌ Remote audio playback error:', error);
      };

      // Test audio context
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const source = audioContext.createMediaStreamSource(stream);
      const analyser = audioContext.createAnalyser();

      source.connect(analyser);
      analyser.fftSize = 256;

      const bufferLength = analyser.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);

      // Test for audio activity
      let testCount = 0;
      const testInterval = setInterval(() => {
        analyser.getByteFrequencyData(dataArray);
        const average = dataArray.reduce((a, b) => a + b) / bufferLength;

        console.log(`🔊 Remote audio level test ${testCount + 1}: ${average.toFixed(2)}`);

        testCount++;
        if (testCount >= 5) {
          clearInterval(testInterval);
          audioContext.close();
          console.log('✅ Remote audio playback test completed');
        }
      }, 1000);

    } catch (error) {
      console.error('❌ Failed to test remote audio playback:', error);
    }
  }
}
