// socket-server/src/tests/encryptionSocket.test.ts
/**
 * Comprehensive tests for Phase 3 encryption socket functionality.
 * Tests encrypted message routing, key exchange, and backward compatibility.
 */

import { describe, test, expect, beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import { Server } from 'socket.io';
import { createServer } from 'http';
import { AddressInfo } from 'net';
import { io as Client, Socket as ClientSocket } from 'socket.io-client';
import jwt from 'jsonwebtoken';
import { PrismaClient } from '@prisma/client';
import { EncryptionService } from '../services/encryptionService';

// Test configuration
const JWT_SECRET = process.env.JWT_SECRET || 'test-secret-key';
const TEST_PORT = 0; // Use random available port

// Test data
const testUsers = [
  {
    id: '550e8400-e29b-41d4-a716-************',
    username: 'alice_test',
    email: '<EMAIL>',
    password: 'hashedpassword1',
    last_login: null,
    is_superuser: false,
    is_staff: false,
    is_active: true,
    date_joined: new Date('2023-01-01T00:00:00Z'),
    firstName: 'Alice',
    lastName: 'Test',
    profilePicture: null,
    isVerified: true,
    lastSeen: new Date('2023-01-01T00:00:00Z'),
    createdAt: new Date('2023-01-01T00:00:00Z'),
    updatedAt: new Date('2023-01-01T00:00:00Z'),
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440002', 
    username: 'bob_test',
    email: '<EMAIL>',
    password: 'hashedpassword2',
    last_login: null,
    is_superuser: false,
    is_staff: false,
    is_active: true,
    date_joined: new Date('2023-01-01T00:00:00Z'),
    firstName: 'Bob',
    lastName: 'Test',
    profilePicture: null,
    isVerified: true,
    lastSeen: new Date('2023-01-01T00:00:00Z'),
    createdAt: new Date('2023-01-01T00:00:00Z'),
    updatedAt: new Date('2023-01-01T00:00:00Z'),
  }
];

const testConversation = {
  id: '550e8400-e29b-41d4-a716-446655440003',
  name: 'Test Encryption Conversation',
  type: 'GROUP',
  createdAt: new Date('2023-01-01T00:00:00Z'),
  updatedAt: new Date('2023-01-01T00:00:00Z'),
};

// Mock encrypted message data
const mockEncryptedMessage = {
  conversationId: testConversation.id,
  encryptedContent: 'dGhpcyBpcyBmYWtlIGVuY3J5cHRlZCBjb250ZW50', // base64 fake encrypted content
  iv: 'cmFuZG9tSVY5NmJpdA==', // base64 fake 96-bit IV
  senderRatchetKey: 'ZmFrZVJhdGNoZXRLZXlTUEtJRm9ybWF0', // base64 fake SPKI key
  messageNumber: 1,
  previousChainLength: 0,
  messageType: 'TEXT',
};

const mockKeyBundle = {
  identityPublicKey: 'ZmFrZUlkZW50aXR5S2V5U1BLSQ==',
  signedPrekeyId: 1,
  signedPrekeyPublic: 'ZmFrZVNpZ25lZFByZUtleVNQS0k=',
  signedPrekeySignature: 'ZmFrZVNpZ25hdHVyZQ==',
};

describe('Socket Server Encryption Tests', () => {
  let httpServer: any;
  let io: Server;
  let prisma: PrismaClient;
  let encryptionService: EncryptionService;
  let serverAddress: string;
  let aliceSocket: ClientSocket;
  let bobSocket: ClientSocket;
  let aliceToken: string;
  let bobToken: string;

  beforeAll(async () => {
    // Initialize Prisma
    prisma = new PrismaClient();
    encryptionService = new EncryptionService(prisma);

    // Create HTTP server and Socket.IO
    httpServer = createServer();
    io = new Server(httpServer, {
      cors: {
        origin: '*',
        methods: ['GET', 'POST'],
      },
    });

    // Start server
    await new Promise<void>((resolve) => {
      httpServer.listen(TEST_PORT, () => {
        const port = (httpServer.address() as AddressInfo).port;
        serverAddress = `http://localhost:${port}`;
        resolve();
      });
    });

    // Create test users and conversation in database
    await setupTestData();

    // Generate JWT tokens
    aliceToken = jwt.sign({ userId: testUsers[0].id }, JWT_SECRET);
    bobToken = jwt.sign({ userId: testUsers[1].id }, JWT_SECRET);
  });

  afterAll(async () => {
    // Cleanup test data
    await cleanupTestData();
    
    // Close connections
    await prisma.$disconnect();
    io.close();
    httpServer.close();
  });

  beforeEach(async () => {
    // Create fresh socket connections for each test
    aliceSocket = Client(serverAddress, {
      auth: { token: aliceToken },
      transports: ['websocket'],
    });

    bobSocket = Client(serverAddress, {
      auth: { token: bobToken },
      transports: ['websocket'],
    });

    // Wait for connections
    await Promise.all([
      new Promise<void>((resolve) => aliceSocket.on('connect', resolve)),
      new Promise<void>((resolve) => bobSocket.on('connect', resolve)),
    ]);

    // Join test conversation
    aliceSocket.emit('join_conversation', { conversationId: testConversation.id });
    bobSocket.emit('join_conversation', { conversationId: testConversation.id });
  });

  afterEach(() => {
    // Disconnect sockets
    aliceSocket.disconnect();
    bobSocket.disconnect();
  });

  describe('Encrypted Message Routing', () => {
    test('should route encrypted messages without server-side decryption', async () => {
      const messageReceived = new Promise<any>((resolve) => {
        bobSocket.on('new_message', resolve);
      });

      const messageSent = new Promise<any>((resolve) => {
        aliceSocket.on('message_sent', resolve);
      });

      // Alice sends encrypted message
      aliceSocket.emit('send_message', {
        ...mockEncryptedMessage,
        tempId: 'temp-123',
      });

      // Wait for responses
      const [receivedMessage, sentConfirmation] = await Promise.all([
        messageReceived,
        messageSent,
      ]);

      // Verify message was routed correctly
      expect(receivedMessage).toMatchObject({
        encryptedContent: mockEncryptedMessage.encryptedContent,
        iv: mockEncryptedMessage.iv,
        senderRatchetKey: mockEncryptedMessage.senderRatchetKey,
        messageNumber: mockEncryptedMessage.messageNumber,
        previousChainLength: mockEncryptedMessage.previousChainLength,
        isEncrypted: true,
      });

      // Verify sender confirmation
      expect(sentConfirmation).toMatchObject({
        tempId: 'temp-123',
        status: 'DELIVERED',
        isEncrypted: true,
      });

      // Verify message was stored encrypted in database
      const dbMessage = await prisma.message.findFirst({
        where: { id: sentConfirmation.messageId },
      });

      expect(dbMessage).toMatchObject({
        content: '', // Empty for encrypted messages
        encryptedContent: mockEncryptedMessage.encryptedContent,
        iv: mockEncryptedMessage.iv,
        senderRatchetKey: mockEncryptedMessage.senderRatchetKey,
      });
    });

    test('should handle plaintext messages for backward compatibility', async () => {
      const messageReceived = new Promise<any>((resolve) => {
        bobSocket.on('new_message', resolve);
      });

      const plaintextMessage = {
        conversationId: testConversation.id,
        content: 'This is a plaintext message',
        messageType: 'TEXT',
      };

      // Alice sends plaintext message
      aliceSocket.emit('send_message', {
        ...plaintextMessage,
        tempId: 'temp-456',
      });

      const receivedMessage = await messageReceived;

      // Verify plaintext message was routed correctly
      expect(receivedMessage).toMatchObject({
        content: plaintextMessage.content,
        isEncrypted: false,
      });

      // Should not have encryption fields
      expect(receivedMessage.encryptedContent).toBeUndefined();
      expect(receivedMessage.iv).toBeUndefined();
    });
  });

  describe('Key Exchange Coordination', () => {
    test('should facilitate key bundle exchange between users', async () => {
      // Create key bundle for Bob
      await prisma.userKeyBundle.create({
        data: {
          userId: testUsers[1].id,
          ...mockKeyBundle,
          createdAt: new Date(),
        },
      });

      const keyExchangeResponse = new Promise<any>((resolve) => {
        aliceSocket.on('key_exchange_response', resolve);
      });

      // Alice requests Bob's key bundle
      aliceSocket.emit('key_exchange_request', {
        targetUserId: testUsers[1].id,
        conversationId: testConversation.id,
        ephemeralPublicKey: 'ZmFrZUVwaGVtZXJhbEtleQ==',
      });

      const response = await keyExchangeResponse;

      // Verify key bundle was returned
      expect(response).toMatchObject({
        success: true,
        keyBundle: {
          identityPublicKey: mockKeyBundle.identityPublicKey,
          signedPrekey: {
            id: mockKeyBundle.signedPrekeyId,
            publicKey: mockKeyBundle.signedPrekeyPublic,
            signature: mockKeyBundle.signedPrekeySignature,
          },
        },
      });
    });

    test('should handle key exchange request for user without encryption', async () => {
      const keyExchangeResponse = new Promise<any>((resolve) => {
        aliceSocket.on('key_exchange_response', resolve);
      });

      // Alice requests key bundle for user without encryption
      aliceSocket.emit('key_exchange_request', {
        targetUserId: testUsers[1].id, // Bob has no key bundle
        conversationId: testConversation.id,
        ephemeralPublicKey: 'ZmFrZUVwaGVtZXJhbEtleQ==',
      });

      const response = await keyExchangeResponse;

      // Verify error response
      expect(response).toMatchObject({
        error: 'Target user does not have encryption enabled',
        code: 'NO_KEY_BUNDLE',
      });
    });
  });

  describe('Encryption Status Checking', () => {
    test('should check conversation encryption status', async () => {
      const statusResponse = new Promise<any>((resolve) => {
        aliceSocket.on('encryption_status_response', resolve);
      });

      // Check encryption status
      aliceSocket.emit('encryption_status_check', {
        conversationId: testConversation.id,
      });

      const response = await statusResponse;

      // Verify status response
      expect(response).toMatchObject({
        conversationId: testConversation.id,
        isEncrypted: false, // No users have key bundles yet
        participants: expect.arrayContaining([
          expect.objectContaining({
            id: testUsers[0].id,
            username: testUsers[0].username,
            hasEncryption: false,
          }),
          expect.objectContaining({
            id: testUsers[1].id,
            username: testUsers[1].username,
            hasEncryption: false,
          }),
        ]),
      });
    });
  });

  describe('Concurrent Connections', () => {
    test('should handle multiple concurrent encrypted messages', async () => {
      const messagesReceived: any[] = [];
      const messagePromises: Promise<any>[] = [];

      // Set up message listeners
      bobSocket.on('new_message', (message) => {
        messagesReceived.push(message);
      });

      // Send multiple encrypted messages concurrently
      for (let i = 0; i < 5; i++) {
        const messagePromise = new Promise<any>((resolve) => {
          const tempId = `temp-${i}`;
          aliceSocket.on('message_sent', (response) => {
            if (response.tempId === tempId) {
              resolve(response);
            }
          });

          aliceSocket.emit('send_message', {
            ...mockEncryptedMessage,
            messageNumber: i + 1,
            tempId,
          });
        });

        messagePromises.push(messagePromise);
      }

      // Wait for all messages to be sent and received
      await Promise.all(messagePromises);
      
      // Give some time for all messages to be received
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verify all messages were received
      expect(messagesReceived).toHaveLength(5);
      
      // Verify messages are encrypted
      messagesReceived.forEach((message, index) => {
        expect(message).toMatchObject({
          encryptedContent: mockEncryptedMessage.encryptedContent,
          messageNumber: index + 1,
          isEncrypted: true,
        });
      });
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid encrypted message data', async () => {
      const errorResponse = new Promise<any>((resolve) => {
        aliceSocket.on('error', resolve);
      });

      // Send invalid encrypted message
      aliceSocket.emit('send_message', {
        conversationId: testConversation.id,
        encryptedContent: 'invalid-base64!',
        iv: 'too-short',
        senderRatchetKey: 'invalid',
        messageNumber: -1,
      });

      const error = await errorResponse;

      expect(error).toMatchObject({
        error: 'Invalid message data',
        code: 'VALIDATION_ERROR',
      });
    });
  });

  // Helper functions
  async function setupTestData() {
    // Create test users
    for (const user of testUsers) {
      await prisma.user.upsert({
        where: { id: user.id },
        update: user,
        create: user,
      });
    }

    // Create test conversation
    await prisma.conversation.upsert({
      where: { id: testConversation.id },
      update: testConversation,
      create: testConversation,
    });

    // Add participants to conversation
    for (const user of testUsers) {
      await prisma.conversationParticipant.upsert({
        where: {
          conversationId_userId: {
            conversationId: testConversation.id,
            userId: user.id,
          },
        },
        update: {},
        create: {
          conversationId: testConversation.id,
          userId: user.id,
          role: 'MEMBER',
          joinedAt: new Date('2023-01-01T00:00:00Z'),
        },
      });
    }
  }

  async function cleanupTestData() {
    // Clean up in reverse order of dependencies
    await prisma.messageKey.deleteMany({});
    await prisma.conversationSession.deleteMany({});
    await prisma.keyBundleUploadLog.deleteMany({});
    await prisma.oneTimePreKey.deleteMany({});
    await prisma.userKeyBundle.deleteMany({});
    await prisma.messageStatus.deleteMany({});
    await prisma.message.deleteMany({});
    await prisma.conversationParticipant.deleteMany({});
    await prisma.conversation.deleteMany({});
    await prisma.user.deleteMany({
      where: {
        id: {
          in: testUsers.map(u => u.id),
        },
      },
    });
  }
});
