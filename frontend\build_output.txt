
> frontend@0.0.0 build
> tsc -b && vite build

src/components/calling/CallManager.tsx(11,3): error TS6133: 'updateCallStatus' is declared but its value is never read.
src/components/calling/CallManager.tsx(14,3): error TS6133: 'startCallTimer' is declared but its value is never read.
src/components/calling/CallManager.tsx(22,19): error TS6133: 'sendWebRTCOffer' is declared but its value is never read.
src/components/calling/CallManager.tsx(22,36): error TS6133: 'sendWebRTCAnswer' is declared but its value is never read.
src/components/Chat/__tests__/ChatRoom.test.tsx(133,5): error TS2322: Type 'null' is not assignable to type '{ id: string; username: string; first_name: string; last_name: string; email: string; }'.
src/components/Chat/__tests__/ConversationList.test.tsx(76,30): error TS6133: 'content' is declared but its value is never read.
src/components/Chat/__tests__/ConversationList.test.tsx(81,30): error TS6133: 'content' is declared but its value is never read.
src/components/Chat/__tests__/EnhancedMediaUpload.test.tsx(12,10): error TS2459: Module '"../../../test/utils"' declares 'render' locally, but it is not exported.
src/components/Chat/__tests__/EnhancedMediaUpload.test.tsx(48,1): error TS2304: Cannot find name 'global'.
src/components/Chat/__tests__/EnhancedMediaUpload.test.tsx(49,1): error TS2304: Cannot find name 'global'.
src/components/Chat/__tests__/EnhancedMediaUpload.test.tsx(52,23): error TS2304: Cannot find name 'global'.
src/components/Chat/__tests__/EnhancedMediaUpload.test.tsx(72,7): error TS2322: Type 'Reducer<EncryptionState>' is not assignable to type 'Reducer<EncryptionState, UnknownAction, { conversationKeys: { 'conv-1': string; }; isInitialized: boolean; loading: boolean; error: null; }>'.
  Types of parameters 'state' and 'state' are incompatible.
    Type 'EncryptionState | { conversationKeys: { 'conv-1': string; }; isInitialized: boolean; loading: boolean; error: null; } | undefined' is not assignable to type 'EncryptionState | undefined'.
      Type '{ conversationKeys: { 'conv-1': string; }; isInitialized: boolean; loading: boolean; error: null; }' is missing the following properties from type 'EncryptionState': identityKeys, signedPreKey, oneTimePreKeys, sessions, and 5 more.
src/components/Chat/__tests__/EnhancedMediaUpload.test.tsx(208,11): error TS6133: 'mockFile' is declared but its value is never read.
src/components/Chat/__tests__/EnhancedMediaUpload.test.tsx(292,11): error TS6133: 'mockFiles' is declared but its value is never read.
src/components/Chat/__tests__/MessageStatus.test.tsx(2,1): error TS6133: 'React' is declared but its value is never read.
src/components/Chat/__tests__/MessageStatus.test.tsx(6,1): error TS6133: 'MessageStatusType' is declared but its value is never read.
src/components/Chat/__tests__/MessageStatus.test.tsx(11,3): error TS2304: Cannot find name 'beforeEach'.
src/components/Chat/__tests__/UserSearch.test.tsx(32,28): error TS6133: 'centered' is declared but its value is never read.
src/components/Chat/__tests__/UserSearch.test.tsx(40,39): error TS6133: 'size' is declared but its value is never read.
src/components/Chat/__tests__/UserSearch.test.tsx(57,43): error TS6133: 'size' is declared but its value is never read.
src/components/Chat/ConversationList.tsx(2,17): error TS6133: 'useEffect' is declared but its value is never read.
src/components/Chat/ConversationList.tsx(30,44): error TS2339: Property 'results' does not exist on type 'PaginatedResponse<Conversation>'.
src/components/Chat/ConversationList.tsx(217,9): error TS2322: Type '{ open: boolean; onClose: () => void; onCreateGroup: (groupData: { name: string; description?: string | undefined; memberIds: string[]; maxParticipants: number; isPublic: boolean; }) => Promise<void>; currentUser: { ...; }; }' is not assignable to type 'IntrinsicAttributes & CreateGroupDialogProps'.
  Property 'onCreateGroup' does not exist on type 'IntrinsicAttributes & CreateGroupDialogProps'.
src/components/Chat/EnhancedMediaUpload.tsx(2,40): error TS6133: 'useEffect' is declared but its value is never read.
src/components/Chat/EnhancedMediaUpload.tsx(26,8): error TS6133: 'MediaUploadFile' is declared but its value is never read.
src/components/Chat/EnhancedMediaUpload.tsx(29,1): error TS6133: 'RootState' is declared but its value is never read.
src/components/Chat/EnhancedMediaUpload.tsx(48,33): error TS6133: 'encryptionReady' is declared but its value is never read.
src/components/Chat/EnhancedMediaUpload.tsx(50,10): error TS6133: 'currentSessionId' is declared but its value is never read.
src/components/Chat/EnhancedMediaUpload.tsx(66,62): error TS6133: 'source' is declared but its value is never read.
src/components/Chat/EnhancedMediaUpload.tsx(261,33): error TS2339: Property 'id' does not exist on type 'MediaUploadResponse'.
src/components/Chat/EnhancedMediaUpload.tsx(268,33): error TS2339: Property 'id' does not exist on type 'MediaUploadResponse'.
src/components/Chat/MediaUpload.tsx(180,29): error TS2339: Property 'id' does not exist on type 'MediaUploadResponse'.
src/components/Chat/MediaUploadDialog.tsx(3,13): error TS6133: 'Upload' is declared but its value is never read.
src/components/Chat/MediaUploadDialog.tsx(236,26): error TS7009: 'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.
src/components/Chat/MediaUploadDialog.tsx(236,43): error TS2554: Expected 1 arguments, but got 3.
src/components/Chat/MessageInput.tsx(30,10): error TS6133: 'uploadingFiles' is declared but its value is never read.
src/components/Chat/MessageInput.tsx(31,10): error TS6133: 'uploadProgress' is declared but its value is never read.
src/components/Chat/MessageInput.tsx(103,9): error TS6133: 'handleUploadStart' is declared but its value is never read.
src/components/Chat/MessageInput.tsx(109,9): error TS6133: 'handleUploadProgress' is declared but its value is never read.
src/components/Chat/MessageItem.tsx(10,15): error TS2305: Module '"../../store"' has no exported member 'MessageStatusType'.
src/components/Chat/MessageItem.tsx(37,3): error TS6133: 'currentUserId' is declared but its value is never read.
src/components/Chat/MessageList.tsx(174,33): error TS2339: Property 'media_files' does not exist on type 'Message | Message'.
  Property 'media_files' does not exist on type 'Message'.
src/components/Chat/MessageList.tsx(176,13): error TS2322: Type '((message: Message) => void) | undefined' is not assignable to type '((message: MessageType) => void) | undefined'.
  Type '(message: Message) => void' is not assignable to type '(message: MessageType) => void'.
    Types of parameters 'message' and 'message' are incompatible.
      Type 'MessageType' is not assignable to type 'Message'.
        Type 'Message' is missing the following properties from type 'Message': conversation_id, message_type, created_at, updated_at
src/components/Chat/SimpleMediaUpload.tsx(3,38): error TS6133: 'Camera' is declared but its value is never read.
src/components/Chat/SimpleMediaUpload.tsx(6,1): error TS6133: 'useMediaEncryption' is declared but its value is never read.
src/components/Chat/SimpleMediaUpload.tsx(41,9): error TS6133: 'sendMessage' is declared but its value is never read.
src/components/Group/CreateGroupDialog.tsx(2,28): error TS6133: 'Check' is declared but its value is never read.
src/components/Group/CreateGroupDialog.tsx(33,19): error TS6133: 'setLoading' is declared but its value is never read.
src/components/Group/GroupSettings.tsx(67,5): error TS2345: Argument of type '{ query: string; limit: number; }' is not assignable to parameter of type 'string | unique symbol'.
src/components/Group/GroupSettings.tsx(454,83): error TS2345: Argument of type '{ id: string; username: string; first_name: string; last_name: string; full_name: string; profile_picture?: string | undefined; }' is not assignable to parameter of type 'User'.
  Type '{ id: string; username: string; first_name: string; last_name: string; full_name: string; profile_picture?: string | undefined; }' is missing the following properties from type 'User': firstName, lastName
src/contexts/__tests__/AuthContext.test.tsx(134,60): error TS2345: Argument of type '{ id: string; username: string; email: string; firstName: string; lastName: string; profilePicture: null; }' is not assignable to parameter of type 'User'.
  Type '{ id: string; username: string; email: string; firstName: string; lastName: string; profilePicture: null; }' is missing the following properties from type 'User': isVerified, lastSeen, createdAt
src/contexts/__tests__/AuthContext.test.tsx(357,60): error TS2345: Argument of type '{ id: string; username: string; email: string; firstName: string; lastName: string; profilePicture: null; }' is not assignable to parameter of type 'User'.
  Type '{ id: string; username: string; email: string; firstName: string; lastName: string; profilePicture: null; }' is missing the following properties from type 'User': isVerified, lastSeen, createdAt
src/contexts/__tests__/AuthContext.test.tsx(501,60): error TS2345: Argument of type '{ id: string; username: string; email: string; firstName: string; lastName: string; profilePicture: null; }' is not assignable to parameter of type 'User'.
  Type '{ id: string; username: string; email: string; firstName: string; lastName: string; profilePicture: null; }' is missing the following properties from type 'User': isVerified, lastSeen, createdAt
src/contexts/__tests__/SocketContext.test.tsx(162,7): error TS2322: Type 'null' is not assignable to type 'string'.
src/contexts/__tests__/SocketContext.test.tsx(518,7): error TS2322: Type 'null' is not assignable to type 'string'.
src/contexts/__tests__/SocketContext.test.tsx(558,7): error TS2322: Type 'null' is not assignable to type 'string'.
src/contexts/EncryptionContext.tsx(9,3): error TS6133: 'encryptMessage' is declared but its value is never read.
src/contexts/EncryptionContext.tsx(14,3): error TS6133: 'generateAESKey' is declared but its value is never read.
src/contexts/SocketContext.tsx(35,3): error TS6133: 'setCallingError' is declared but its value is never read.
src/contexts/SocketContext.tsx(95,61): error TS6133: 'isEncryptionReady' is declared but its value is never read.
src/contexts/SocketContext.tsx(627,7): error TS2353: Object literal may only specify known properties, and 'message' does not exist in type 'Message & { tempId?: string | undefined; }'.
src/contexts/SocketContext.tsx(633,7): error TS2353: Object literal may only specify known properties, and 'lastMessage' does not exist in type '{ conversationId: string; message: { id: string; content: string; sender: { username: string; }; createdAt: string; }; }'.
src/contexts/SocketContext.tsx(688,52): error TS2339: Property 'id' does not exist on type 'Conversation | ApiResponse<Conversation>'.
  Property 'id' does not exist on type 'ApiResponse<Conversation>'.
src/contexts/SocketContext.tsx(689,53): error TS2339: Property 'id' does not exist on type 'Conversation | ApiResponse<Conversation>'.
  Property 'id' does not exist on type 'ApiResponse<Conversation>'.
src/contexts/SocketContext.tsx(694,15): error TS2322: Type 'Conversation | ApiResponse<Conversation>' is not assignable to type 'Conversation'.
  Type 'ApiResponse<Conversation>' is missing the following properties from type 'Conversation': id, type, participants, createdAt, updatedAt
src/contexts/SocketContext.tsx(717,52): error TS2339: Property 'id' does not exist on type 'Conversation | ApiResponse<Conversation>'.
  Property 'id' does not exist on type 'ApiResponse<Conversation>'.
src/contexts/SocketContext.tsx(718,53): error TS2339: Property 'id' does not exist on type 'Conversation | ApiResponse<Conversation>'.
  Property 'id' does not exist on type 'ApiResponse<Conversation>'.
src/contexts/SocketContext.tsx(723,15): error TS2322: Type 'Conversation | ApiResponse<Conversation>' is not assignable to type 'Conversation'.
  Type 'ApiResponse<Conversation>' is missing the following properties from type 'Conversation': id, type, participants, createdAt, updatedAt
src/crypto/keyManager.ts(32,3): error TS6196: 'IdentityKeyPair' is declared but never used.
src/crypto/keyManager.ts(33,3): error TS6196: 'PreKeyPair' is declared but never used.
src/crypto/keyManager.ts(34,3): error TS6196: 'EncryptionErrorCode' is declared but never used.
src/crypto/keyStorage.ts(12,3): error TS6196: 'EncryptionError' is declared but never used.
src/crypto/keyStorage.ts(13,3): error TS6196: 'EncryptionErrorCode' is declared but never used.
src/crypto/webCrypto.ts(14,3): error TS6196: 'EncryptionError' is declared but never used.
src/crypto/webCrypto.ts(64,5): error TS1294: This syntax is not allowed when 'erasableSyntaxOnly' is enabled.
src/crypto/webCrypto.ts(66,5): error TS1294: This syntax is not allowed when 'erasableSyntaxOnly' is enabled.
src/crypto/webCrypto.ts(514,3): error TS6133: 'preKeyId' is declared but its value is never read.
src/hooks/__tests__/useDebounce.test.ts(215,38): error TS2304: Cannot find name 'global'.
src/hooks/__tests__/useDebounce.test.ts(241,16): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/hooks/useApiError.ts(3,1): error TS6133: 'BaseQueryError' is declared but its value is never read.
src/hooks/useMessageDecryption.ts(41,21): error TS2339: Property 'isEncrypted' does not exist on type 'MessageType'.
  Property 'isEncrypted' does not exist on type 'Message'.
src/hooks/useMessageDecryption.ts(41,44): error TS2339: Property 'encryptedContent' does not exist on type 'MessageType'.
  Property 'encryptedContent' does not exist on type 'Message'.
src/hooks/useMessageDecryption.ts(41,72): error TS2339: Property 'iv' does not exist on type 'MessageType'.
  Property 'iv' does not exist on type 'Message'.
src/hooks/useMessageDecryption.ts(41,86): error TS2339: Property 'senderRatchetKey' does not exist on type 'MessageType'.
  Property 'senderRatchetKey' does not exist on type 'Message'.
src/hooks/useMessageDecryption.ts(44,44): error TS2339: Property 'encryptedContent' does not exist on type 'MessageType'.
  Property 'encryptedContent' does not exist on type 'Message'.
src/hooks/useMessageDecryption.ts(45,30): error TS2339: Property 'iv' does not exist on type 'MessageType'.
  Property 'iv' does not exist on type 'Message'.
src/hooks/useMessageDecryption.ts(46,44): error TS2339: Property 'senderRatchetKey' does not exist on type 'MessageType'.
  Property 'senderRatchetKey' does not exist on type 'Message'.
src/hooks/useMessageDecryption.ts(52,21): error TS2339: Property 'encryptedContent' does not exist on type 'MessageType'.
  Property 'encryptedContent' does not exist on type 'Message'.
src/hooks/useMessageDecryption.ts(53,21): error TS2339: Property 'iv' does not exist on type 'MessageType'.
  Property 'iv' does not exist on type 'Message'.
src/hooks/useMessageDecryption.ts(54,21): error TS2339: Property 'senderRatchetKey' does not exist on type 'MessageType'.
  Property 'senderRatchetKey' does not exist on type 'Message'.
src/hooks/useMessageDecryption.ts(59,28): error TS2339: Property 'isEncrypted' does not exist on type 'MessageType'.
  Property 'isEncrypted' does not exist on type 'Message'.
src/hooks/useMessageDecryption.ts(63,44): error TS2339: Property 'encryptedContent' does not exist on type 'MessageType'.
  Property 'encryptedContent' does not exist on type 'Message'.
src/hooks/useMessageDecryption.ts(64,30): error TS2339: Property 'iv' does not exist on type 'MessageType'.
  Property 'iv' does not exist on type 'Message'.
src/hooks/useMessageDecryption.ts(65,44): error TS2339: Property 'senderRatchetKey' does not exist on type 'MessageType'.
  Property 'senderRatchetKey' does not exist on type 'Message'.
src/hooks/useMessageDecryption.ts(83,44): error TS2339: Property 'encryptedContent' does not exist on type 'MessageType'.
  Property 'encryptedContent' does not exist on type 'Message'.
src/hooks/useMessageDecryption.ts(83,70): error TS2339: Property 'iv' does not exist on type 'MessageType'.
  Property 'iv' does not exist on type 'Message'.
src/hooks/useMessageDecryption.ts(83,82): error TS2339: Property 'senderRatchetKey' does not exist on type 'MessageType'.
  Property 'senderRatchetKey' does not exist on type 'Message'.
src/hooks/useMessageDecryption.ts(83,108): error TS2339: Property 'isEncrypted' does not exist on type 'MessageType'.
  Property 'isEncrypted' does not exist on type 'Message'.
src/hooks/useSocketCacheSync.ts(66,34): error TS2345: Argument of type '"getMessages"' is not assignable to parameter of type '"test"'.
src/hooks/useSocketCacheSync.ts(68,48): error TS7006: Parameter 'msg' implicitly has an 'any' type.
src/pages/Dashboard.tsx(4,1): error TS6133: 'Link' is declared but its value is never read.
src/pages/Dashboard.tsx(28,38): error TS6133: 'conversationId' is declared but its value is never read.
src/pages/Login.tsx(132,11): error TS2322: Type 'void' is not assignable to type 'ReactNode'.
src/pages/Register.tsx(55,9): error TS2561: Object literal may only specify known properties, but 'first_name' does not exist in type 'RegisterData'. Did you mean to write 'firstName'?
src/services/__tests__/api.test.ts(20,1): error TS2304: Cannot find name 'global'.
src/services/__tests__/api.test.ts(70,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/api.test.ts(73,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/api.test.ts(82,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<any, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/api.test.ts(85,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/api.test.ts(94,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<FormData, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/api.test.ts(97,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/api.test.ts(104,43): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/api.test.ts(105,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/api.test.ts(114,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/api.test.ts(118,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/api.test.ts(124,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/api.test.ts(128,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/api.test.ts(134,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/api.test.ts(137,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/api.test.ts(144,44): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/api.test.ts(145,44): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/api.test.ts(147,22): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/api.test.ts(147,44): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/api.test.ts(152,48): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/api.test.ts(153,51): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<any, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/api.test.ts(155,26): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/api.test.ts(156,29): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/api.test.ts(164,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<any, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/api.test.ts(167,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/api.test.ts(178,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<FormData, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/api.test.ts(181,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/api.test.ts(191,43): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/api.test.ts(194,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/api.test.ts(199,43): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/api.test.ts(201,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/api.test.ts(209,14): error TS18046: 'state' is of type 'unknown'.
src/services/__tests__/api.test.ts(210,14): error TS18046: 'state' is of type 'unknown'.
src/services/__tests__/api.test.ts(216,24): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/api.test.ts(217,24): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<any, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/api.test.ts(218,24): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<FormData, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/api.test.ts(225,23): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/authApi.test.ts(8,1): error TS6133: 'mockUsers' is declared but its value is never read.
src/services/__tests__/authApi.test.ts(8,10): error TS2459: Module '"../../test/mocks/handlers"' declares 'mockUsers' locally, but it is not exported.
src/services/__tests__/authApi.test.ts(30,1): error TS2304: Cannot find name 'global'.
src/services/__tests__/authApi.test.ts(53,13): error TS6133: 'mockBackendResponse' is declared but its value is never read.
src/services/__tests__/authApi.test.ts(80,65): error TS2345: Argument of type '{ id: string; username: string; email: string; firstName: string; lastName: string; profilePicture: null; }' is not assignable to parameter of type 'User'.
  Type '{ id: string; username: string; email: string; firstName: string; lastName: string; profilePicture: null; }' is missing the following properties from type 'User': isVerified, lastSeen, createdAt
src/services/__tests__/authApi.test.ts(83,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<LoginRequest, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", ApiResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/authApi.test.ts(89,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/authApi.test.ts(116,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<LoginRequest, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", ApiResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/authApi.test.ts(123,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/authApi.test.ts(124,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/authApi.test.ts(130,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<LoginRequest, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", ApiResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/authApi.test.ts(137,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/authApi.test.ts(138,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/authApi.test.ts(144,13): error TS6133: 'mockBackendResponse' is declared but its value is never read.
src/services/__tests__/authApi.test.ts(171,65): error TS2345: Argument of type '{ id: string; username: string; email: string; firstName: string; lastName: string; profilePicture: null; }' is not assignable to parameter of type 'User'.
  Type '{ id: string; username: string; email: string; firstName: string; lastName: string; profilePicture: null; }' is missing the following properties from type 'User': isVerified, lastSeen, createdAt
src/services/__tests__/authApi.test.ts(174,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<RegisterRequest, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | ... 4 more ... | "CallHistory", ApiResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/authApi.test.ts(183,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/authApi.test.ts(206,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<RegisterRequest, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | ... 4 more ... | "CallHistory", ApiResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/authApi.test.ts(216,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/authApi.test.ts(217,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/authApi.test.ts(226,43): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", void, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/authApi.test.ts(228,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/authApi.test.ts(246,13): error TS6133: 'mockBackendResponse' is declared but its value is never read.
src/services/__tests__/authApi.test.ts(260,65): error TS2345: Argument of type '{ id: string; username: string; email: string; firstName: string; lastName: string; profilePicture: null; }' is not assignable to parameter of type 'User'.
  Type '{ id: string; username: string; email: string; firstName: string; lastName: string; profilePicture: null; }' is missing the following properties from type 'User': isVerified, lastSeen, createdAt
src/services/__tests__/authApi.test.ts(262,43): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", ApiResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/authApi.test.ts(264,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/authApi.test.ts(281,43): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", ApiResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/authApi.test.ts(283,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/authApi.test.ts(284,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/conversationApi.test.ts(10,1): error TS2304: Cannot find name 'global'.
src/services/__tests__/conversationApi.test.ts(24,9): error TS2322: Type 'null' is not assignable to type 'string | undefined'.
src/services/__tests__/conversationApi.test.ts(31,9): error TS2322: Type 'null' is not assignable to type 'string | undefined'.
src/services/__tests__/conversationApi.test.ts(54,9): error TS2322: Type 'null' is not assignable to type 'string | undefined'.
src/services/__tests__/conversationApi.test.ts(61,9): error TS2322: Type 'null' is not assignable to type 'string | undefined'.
src/services/__tests__/conversationApi.test.ts(68,9): error TS2322: Type 'null' is not assignable to type 'string | undefined'.
src/services/__tests__/conversationApi.test.ts(94,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", PaginatedResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/conversationApi.test.ts(97,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/conversationApi.test.ts(107,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", PaginatedResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/conversationApi.test.ts(110,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/conversationApi.test.ts(121,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", PaginatedResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/conversationApi.test.ts(124,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/conversationApi.test.ts(143,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<CreateConversationRequest, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | ... 5 more ... | "CallHistory", ApiResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/conversationApi.test.ts(149,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/conversationApi.test.ts(157,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<CreateConversationRequest, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | ... 5 more ... | "CallHistory", ApiResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/conversationApi.test.ts(164,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/conversationApi.test.ts(173,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<CreateConversationRequest, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | ... 5 more ... | "CallHistory", ApiResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/conversationApi.test.ts(179,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/conversationApi.test.ts(180,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/conversationApi.test.ts(198,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<CreateConversationRequest, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | ... 5 more ... | "CallHistory", ApiResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/conversationApi.test.ts(206,27): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/conversationApi.test.ts(207,27): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/conversationApi.test.ts(208,27): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/conversationApi.test.ts(232,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", PaginatedResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/conversationApi.test.ts(242,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<{ conversation: Conversation; }, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | ... 4 more ... | "CallHistory", void, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/conversationApi.test.ts(247,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/conversationApi.test.ts(250,78): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'RootState<{ test: QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>; testMutation: MutationDefinition<...>; upload: MutationDefinition<...>; } & { ...; }, "User" | ... 5 more ...'.
src/services/__tests__/conversationApi.test.ts(251,31): error TS2339: Property 'results' does not exist on type 'PaginatedResponse<Conversation>'.
src/services/__tests__/conversationApi.test.ts(252,45): error TS2339: Property 'results' does not exist on type 'PaginatedResponse<Conversation>'.
src/services/__tests__/conversationApi.test.ts(272,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", PaginatedResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/conversationApi.test.ts(277,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<{ conversation: Conversation; }, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | ... 4 more ... | "CallHistory", void, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/conversationApi.test.ts(283,78): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'RootState<{ test: QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>; testMutation: MutationDefinition<...>; upload: MutationDefinition<...>; } & { ...; }, "User" | ... 5 more ...'.
src/services/__tests__/conversationApi.test.ts(284,31): error TS2339: Property 'results' does not exist on type 'PaginatedResponse<Conversation>'.
src/services/__tests__/conversationApi.test.ts(285,45): error TS2339: Property 'results' does not exist on type 'PaginatedResponse<Conversation>'.
src/services/__tests__/conversationApi.test.ts(307,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", PaginatedResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/conversationApi.test.ts(319,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<{ conversationId: string; lastMessage: { id: string; content: string; sender: { username: string; }; createdAt: string; } | undefined; }, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | ... 5 more ... | "CallHistory", void, "api", unk...' is not assignable to parameter of type 'Action'.
src/services/__tests__/conversationApi.test.ts(326,78): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'RootState<{ test: QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>; testMutation: MutationDefinition<...>; upload: MutationDefinition<...>; } & { ...; }, "User" | ... 5 more ...'.
src/services/__tests__/conversationApi.test.ts(327,46): error TS2339: Property 'results' does not exist on type 'PaginatedResponse<Conversation>'.
src/services/__tests__/conversationApi.test.ts(350,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", PaginatedResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/conversationApi.test.ts(355,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<{ conversationId: string; lastMessage: { id: string; content: string; sender: { username: string; }; createdAt: string; } | undefined; }, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | ... 5 more ... | "CallHistory", void, "api", unk...' is not assignable to parameter of type 'Action'.
src/services/__tests__/conversationApi.test.ts(367,78): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'RootState<{ test: QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>; testMutation: MutationDefinition<...>; upload: MutationDefinition<...>; } & { ...; }, "User" | ... 5 more ...'.
src/services/__tests__/conversationApi.test.ts(368,31): error TS2339: Property 'results' does not exist on type 'PaginatedResponse<Conversation>'.
src/services/__tests__/conversationApi.test.ts(369,45): error TS2339: Property 'results' does not exist on type 'PaginatedResponse<Conversation>'.
src/services/__tests__/conversationApi.test.ts(377,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", PaginatedResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/conversationApi.test.ts(381,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/conversationApi.test.ts(382,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/conversationApi.test.ts(388,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", PaginatedResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/conversationApi.test.ts(392,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/messageApi.test.ts(10,1): error TS2304: Cannot find name 'global'.
src/services/__tests__/messageApi.test.ts(24,7): error TS2322: Type 'null' is not assignable to type 'string | undefined'.
src/services/__tests__/messageApi.test.ts(35,9): error TS6133: 'mockPaginatedResponse' is declared but its value is never read.
src/services/__tests__/messageApi.test.ts(61,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<FetchMessagesRequest, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | ... 4 more ... | "CallHistory", MessagesPaginatedResponse, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/messageApi.test.ts(67,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/messageApi.test.ts(76,13): error TS6133: 'page1Response' is declared but its value is never read.
src/services/__tests__/messageApi.test.ts(89,13): error TS6133: 'page2Response' is declared but its value is never read.
src/services/__tests__/messageApi.test.ts(98,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<FetchMessagesRequest, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | ... 4 more ... | "CallHistory", MessagesPaginatedResponse, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/messageApi.test.ts(104,22): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/messageApi.test.ts(108,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<FetchMessagesRequest, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | ... 4 more ... | "CallHistory", MessagesPaginatedResponse, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/messageApi.test.ts(115,22): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/messageApi.test.ts(116,22): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/messageApi.test.ts(122,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<FetchMessagesRequest, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | ... 4 more ... | "CallHistory", MessagesPaginatedResponse, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/messageApi.test.ts(127,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/messageApi.test.ts(128,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/messageApi.test.ts(171,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<SendMessageRequest, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | ... 4 more ... | "CallHistory", ApiResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/messageApi.test.ts(178,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/messageApi.test.ts(187,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<SendMessageRequest, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | ... 4 more ... | "CallHistory", ApiResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/messageApi.test.ts(193,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/messageApi.test.ts(194,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/messageApi.test.ts(212,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<SendMessageRequest, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | ... 4 more ... | "CallHistory", ApiResponse<...>, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/messageApi.test.ts(219,25): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/messageApi.test.ts(220,25): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/messageApi.test.ts(221,25): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/messageApi.test.ts(229,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<FetchMessagesRequest, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | ... 4 more ... | "CallHistory", MessagesPaginatedResponse, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/messageApi.test.ts(242,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<{ conversationId: string; message: Message; tempId?: string | undefined; }, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | ... 5 more ... | "CallHistory", void, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/messageApi.test.ts(248,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/messageApi.test.ts(253,10): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'RootState<{ test: QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>; testMutation: MutationDefinition<...>; upload: MutationDefinition<...>; } & { ...; }, "User" | ... 5 more ...'.
src/services/__tests__/messageApi.test.ts(263,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<FetchMessagesRequest, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | ... 4 more ... | "CallHistory", MessagesPaginatedResponse, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/messageApi.test.ts(270,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<{ conversationId: string; message: Message; tempId?: string | undefined; }, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | ... 5 more ... | "CallHistory", void, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/messageApi.test.ts(279,10): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'RootState<{ test: QueryDefinition<void, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", any, "api", unknown>; testMutation: MutationDefinition<...>; upload: MutationDefinition<...>; } & { ...; }, "User" | ... 5 more ...'.
src/services/__tests__/messageApi.test.ts(291,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<FetchMessagesRequest, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | ... 4 more ... | "CallHistory", MessagesPaginatedResponse, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/messageApi.test.ts(304,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<FetchMessagesRequest, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | ... 4 more ... | "CallHistory", MessagesPaginatedResponse, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/messageApi.test.ts(310,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/userApi.test.ts(10,1): error TS2304: Cannot find name 'global'.
src/services/__tests__/userApi.test.ts(15,9): error TS6133: 'mockUser' is declared but its value is never read.
src/services/__tests__/userApi.test.ts(21,5): error TS2322: Type 'null' is not assignable to type 'string | undefined'.
src/services/__tests__/userApi.test.ts(24,9): error TS6133: 'mockUser2' is declared but its value is never read.
src/services/__tests__/userApi.test.ts(53,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<string, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", SearchUsersResponse, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/userApi.test.ts(57,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/userApi.test.ts(58,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/userApi.test.ts(59,35): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/userApi.test.ts(64,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<string, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", SearchUsersResponse, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/userApi.test.ts(73,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<string, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", SearchUsersResponse, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/userApi.test.ts(83,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<string, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", SearchUsersResponse, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/userApi.test.ts(86,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/userApi.test.ts(87,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/userApi.test.ts(93,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<string, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", SearchUsersResponse, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/userApi.test.ts(96,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/userApi.test.ts(105,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<string, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", SearchUsersResponse, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/userApi.test.ts(108,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/userApi.test.ts(143,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<string, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", { ...; }, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/userApi.test.ts(147,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/userApi.test.ts(148,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/userApi.test.ts(149,21): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/userApi.test.ts(155,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<string, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", { ...; }, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/userApi.test.ts(180,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<Partial<Pick<SearchUser, "username" | "first_name" | "last_name">>, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | ... 5 more ... | "CallHistory", { ...; }, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/userApi.test.ts(194,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<Partial<Pick<SearchUser, "username" | "first_name" | "last_name">>, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | ... 5 more ... | "CallHistory", { ...; }, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/userApi.test.ts(206,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<Partial<Pick<SearchUser, "username" | "first_name" | "last_name">>, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | ... 5 more ... | "CallHistory", { ...; }, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/userApi.test.ts(230,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<Partial<Pick<SearchUser, "username" | "first_name" | "last_name">>, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | ... 5 more ... | "CallHistory", { ...; }, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/userApi.test.ts(244,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<string, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", SearchUsersResponse, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/userApi.test.ts(254,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<string, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", { ...; }, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/userApi.test.ts(264,9): error TS2345: Argument of type 'ThunkAction<MutationActionCreatorResult<MutationDefinition<Partial<Pick<SearchUser, "username" | "first_name" | "last_name">>, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | ... 5 more ... | "CallHistory", { ...; }, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/userApi.test.ts(276,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<string, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", SearchUsersResponse, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/userApi.test.ts(288,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<string, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", SearchUsersResponse, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/userApi.test.ts(292,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<string, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", SearchUsersResponse, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/userApi.test.ts(296,22): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/userApi.test.ts(297,22): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/userApi.test.ts(298,22): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/userApi.test.ts(299,22): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/userApi.test.ts(303,9): error TS2345: Argument of type 'ThunkAction<QueryActionCreatorResult<QueryDefinition<string, BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>, "User" | "Conversation" | "Message" | "Auth" | "Encryption" | "Call" | "CallHistory", SearchUsersResponse, "api", unknown>>, any, any, UnknownAction>' is not assignable to parameter of type 'Action'.
src/services/__tests__/userApi.test.ts(306,22): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/__tests__/userApi.test.ts(307,22): error TS2339: Property 'data' does not exist on type 'Action'.
src/services/api.ts(4,1): error TS6192: All imports in import declaration are unused.
src/services/api.ts(9,14): error TS2580: Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.
src/services/api.ts(9,41): error TS2580: Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.
src/services/authApi.ts(4,3): error TS2305: Module '"../types"' has no exported member 'LoginRequest'.
src/services/cacheUtils.ts(25,35): error TS2345: Argument of type '"getMessages"' is not assignable to parameter of type '"test"'.
src/services/cacheUtils.ts(28,46): error TS7006: Parameter 'msg' implicitly has an 'any' type.
src/services/cacheUtils.ts(38,35): error TS2345: Argument of type '"getConversations"' is not assignable to parameter of type '"test"'.
src/services/cacheUtils.ts(40,50): error TS7006: Parameter 'conv' implicitly has an 'any' type.
src/services/cacheUtils.ts(56,35): error TS2345: Argument of type '"getConversations"' is not assignable to parameter of type '"test"'.
src/services/cacheUtils.ts(58,52): error TS7006: Parameter 'conv' implicitly has an 'any' type.
src/services/cacheUtils.ts(79,35): error TS2345: Argument of type '"getMessages"' is not assignable to parameter of type '"test"'.
src/services/cacheUtils.ts(91,35): error TS2345: Argument of type '"getMessages"' is not assignable to parameter of type '"test"'.
src/services/cacheUtils.ts(93,50): error TS7006: Parameter 'msg' implicitly has an 'any' type.
src/services/cacheUtils.ts(102,35): error TS2345: Argument of type '"getMessages"' is not assignable to parameter of type '"test"'.
src/services/cacheUtils.ts(104,50): error TS7006: Parameter 'msg' implicitly has an 'any' type.
src/services/cacheUtils.ts(114,28): error TS2345: Argument of type '"getMessages"' is not assignable to parameter of type '"test"'.
src/services/callingApi.ts(73,22): error TS6133: 'result' is declared but its value is never read.
src/services/callingApi.ts(73,30): error TS6133: 'error' is declared but its value is never read.
src/services/callingApi.ts(90,30): error TS6133: 'error' is declared but its value is never read.
src/services/callingApi.ts(135,25): error TS6133: 'result' is declared but its value is never read.
src/services/callingApi.ts(135,33): error TS6133: 'error' is declared but its value is never read.
src/services/callingApi.ts(156,25): error TS6133: 'result' is declared but its value is never read.
src/services/callingApi.ts(156,33): error TS6133: 'error' is declared but its value is never read.
src/services/callingApi.ts(177,25): error TS6133: 'result' is declared but its value is never read.
src/services/callingApi.ts(177,33): error TS6133: 'error' is declared but its value is never read.
src/services/callingApi.ts(218,25): error TS6133: 'result' is declared but its value is never read.
src/services/callingApi.ts(218,33): error TS6133: 'error' is declared but its value is never read.
src/services/callingApi.ts(238,25): error TS6133: 'result' is declared but its value is never read.
src/services/callingApi.ts(238,33): error TS6133: 'error' is declared but its value is never read.
src/services/callingApi.ts(257,25): error TS6133: 'result' is declared but its value is never read.
src/services/callingApi.ts(257,33): error TS6133: 'error' is declared but its value is never read.
src/services/conversationApi.ts(83,30): error TS6133: 'conversationData' is declared but its value is never read.
src/services/conversationApi.ts(180,25): error TS6133: 'result' is declared but its value is never read.
src/services/conversationApi.ts(180,33): error TS6133: 'error' is declared but its value is never read.
src/services/conversationApi.ts(196,25): error TS6133: 'result' is declared but its value is never read.
src/services/conversationApi.ts(196,33): error TS6133: 'error' is declared but its value is never read.
src/services/conversationApi.ts(210,25): error TS6133: 'result' is declared but its value is never read.
src/services/conversationApi.ts(210,33): error TS6133: 'error' is declared but its value is never read.
src/services/conversationApi.ts(221,25): error TS6133: 'result' is declared but its value is never read.
src/services/conversationApi.ts(221,33): error TS6133: 'error' is declared but its value is never read.
src/services/encryptionApi.ts(51,22): error TS6133: 'result' is declared but its value is never read.
src/services/encryptionApi.ts(51,30): error TS6133: 'error' is declared but its value is never read.
src/services/messageApi.ts(52,22): error TS6133: 'result' is declared but its value is never read.
src/services/messageApi.ts(52,30): error TS6133: 'error' is declared but its value is never read.
src/services/messageApi.ts(94,30): error TS6133: 'conversationId' is declared but its value is never read.
src/services/messageApi.ts(123,25): error TS6133: 'result' is declared but its value is never read.
src/services/messageApi.ts(123,33): error TS6133: 'error' is declared but its value is never read.
src/services/messageApi.ts(129,109): error TS6133: 'getState' is declared but its value is never read.
src/services/messageApi.ts(185,25): error TS2339: Property 'data' does not exist on type 'MaybeDrafted<MessagesPaginatedResponse>'.
  Property 'data' does not exist on type 'MessagesPaginatedResponse'.
src/services/messageApi.ts(185,39): error TS2339: Property 'data' does not exist on type 'MaybeDrafted<MessagesPaginatedResponse>'.
  Property 'data' does not exist on type 'MessagesPaginatedResponse'.
src/services/messageApi.ts(186,37): error TS2339: Property 'data' does not exist on type 'MaybeDrafted<MessagesPaginatedResponse>'.
  Property 'data' does not exist on type 'MessagesPaginatedResponse'.
src/services/messageApi.ts(186,60): error TS7006: Parameter 'msg' implicitly has an 'any' type.
src/services/messageApi.ts(188,25): error TS2339: Property 'data' does not exist on type 'MaybeDrafted<MessagesPaginatedResponse>'.
  Property 'data' does not exist on type 'MessagesPaginatedResponse'.
src/services/messageApi.ts(223,75): error TS2339: Property 'isOptimistic' does not exist on type 'Message'.
src/services/messageApi.ts(226,38): error TS2339: Property 'isOptimistic' does not exist on type 'Message'.
src/services/messageApi.ts(244,75): error TS2339: Property 'isOptimistic' does not exist on type 'Message'.
src/services/messageApi.ts(246,57): error TS2339: Property 'isOptimistic' does not exist on type 'Message'.
src/services/messageApi.ts(253,123): error TS2339: Property 'isOptimistic' does not exist on type 'Message'.
src/services/messageApi.ts(257,23): error TS2339: Property 'isOptimistic' does not exist on type 'Message'.
src/services/userApi.ts(3,15): error TS6196: 'SearchUsersRequest' is declared but never used.
src/services/userApi.ts(21,22): error TS6133: 'result' is declared but its value is never read.
src/services/userApi.ts(21,30): error TS6133: 'error' is declared but its value is never read.
src/services/userApi.ts(46,22): error TS6133: 'result' is declared but its value is never read.
src/services/userApi.ts(46,30): error TS6133: 'error' is declared but its value is never read.
src/services/webrtc/SignalingService.ts(37,31): error TS6133: 'dispatch' is declared but its value is never read.
src/store/__tests__/index.test.ts(74,33): error TS2345: Argument of type '{ id: string; conversationId: string; sender: { id: string; username: string; first_name: string; last_name: string; profile_picture: null; }; content: string; messageType: "TEXT"; createdAt: string; updatedAt: string; }' is not assignable to parameter of type 'Message & { tempId?: string | undefined; }'.
  Type '{ id: string; conversationId: string; sender: { id: string; username: string; first_name: string; last_name: string; profile_picture: null; }; content: string; messageType: "TEXT"; createdAt: string; updatedAt: string; }' is not assignable to type 'Message'.
    The types of 'sender.profile_picture' are incompatible between these types.
      Type 'null' is not assignable to type 'string | undefined'.
src/store/__tests__/index.test.ts(131,11): error TS2322: Type 'null' is not assignable to type 'string | undefined'.
src/store/__tests__/index.test.ts(228,13): error TS2322: Type 'null' is not assignable to type 'string | undefined'.
src/store/__tests__/index.test.ts(249,29): error TS2580: Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.
src/store/__tests__/index.test.ts(261,13): error TS2322: Type 'null' is not assignable to type 'string | undefined'.
src/store/__tests__/index.test.ts(270,27): error TS2580: Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.
src/store/slices/__tests__/conversationSlice.test.ts(25,9): error TS6133: 'mockDraftConversation' is declared but its value is never read.
src/store/slices/__tests__/conversationSlice.test.ts(28,20): error TS2322: Type '{ id: string; username: string; first_name: string; last_name: string; profile_picture: null; }' is not assignable to type '{ id: string; username: string; first_name: string; last_name: string; profile_picture?: string | undefined; }'.
  Types of property 'profile_picture' are incompatible.
    Type 'null' is not assignable to type 'string | undefined'.
src/store/slices/__tests__/conversationSlice.test.ts(53,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/conversationSlice.test.ts(62,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/conversationSlice.test.ts(70,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/conversationSlice.test.ts(78,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/conversationSlice.test.ts(88,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/conversationSlice.test.ts(95,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/conversationSlice.test.ts(110,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/conversationSlice.test.ts(139,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/conversationSlice.test.ts(163,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/conversationSlice.test.ts(190,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/conversationSlice.test.ts(191,48): error TS7006: Parameter 'draft' implicitly has an 'any' type.
src/store/slices/__tests__/conversationSlice.test.ts(209,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/conversationSlice.test.ts(230,22): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/conversationSlice.test.ts(236,22): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/conversationSlice.test.ts(243,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/conversationSlice.test.ts(265,22): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/conversationSlice.test.ts(271,22): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/conversationSlice.test.ts(300,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/conversationSlice.test.ts(307,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/conversationSlice.test.ts(322,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/conversationSlice.test.ts(335,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/conversationSlice.test.ts(350,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.selectors.test.ts(12,1): error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.
src/store/slices/__tests__/messageSlice.selectors.test.ts(47,3): error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.
src/store/slices/__tests__/messageSlice.selectors.test.ts(48,5): error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.
src/store/slices/__tests__/messageSlice.selectors.test.ts(50,7): error TS2304: Cannot find name 'expect'.
src/store/slices/__tests__/messageSlice.selectors.test.ts(51,7): error TS2304: Cannot find name 'expect'.
src/store/slices/__tests__/messageSlice.selectors.test.ts(54,5): error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.
src/store/slices/__tests__/messageSlice.selectors.test.ts(56,7): error TS2304: Cannot find name 'expect'.
src/store/slices/__tests__/messageSlice.selectors.test.ts(59,5): error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.
src/store/slices/__tests__/messageSlice.selectors.test.ts(62,7): error TS2304: Cannot find name 'expect'.
src/store/slices/__tests__/messageSlice.selectors.test.ts(65,5): error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.
src/store/slices/__tests__/messageSlice.selectors.test.ts(68,7): error TS2304: Cannot find name 'expect'.
src/store/slices/__tests__/messageSlice.selectors.test.ts(72,3): error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.
src/store/slices/__tests__/messageSlice.selectors.test.ts(73,5): error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.
src/store/slices/__tests__/messageSlice.selectors.test.ts(75,7): error TS2304: Cannot find name 'expect'.
src/store/slices/__tests__/messageSlice.selectors.test.ts(77,7): error TS2304: Cannot find name 'expect'.
src/store/slices/__tests__/messageSlice.selectors.test.ts(78,7): error TS2304: Cannot find name 'expect'.
src/store/slices/__tests__/messageSlice.selectors.test.ts(81,5): error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.
src/store/slices/__tests__/messageSlice.selectors.test.ts(84,7): error TS2304: Cannot find name 'expect'.
src/store/slices/__tests__/messageSlice.selectors.test.ts(87,5): error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.
src/store/slices/__tests__/messageSlice.selectors.test.ts(89,7): error TS2304: Cannot find name 'expect'.
src/store/slices/__tests__/messageSlice.selectors.test.ts(93,3): error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.
src/store/slices/__tests__/messageSlice.selectors.test.ts(94,5): error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.
src/store/slices/__tests__/messageSlice.selectors.test.ts(96,7): error TS2304: Cannot find name 'expect'.
src/store/slices/__tests__/messageSlice.selectors.test.ts(99,5): error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.
src/store/slices/__tests__/messageSlice.selectors.test.ts(101,7): error TS2304: Cannot find name 'expect'.
src/store/slices/__tests__/messageSlice.selectors.test.ts(104,5): error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.
src/store/slices/__tests__/messageSlice.selectors.test.ts(107,7): error TS2304: Cannot find name 'expect'.
src/store/slices/__tests__/messageSlice.selectors.test.ts(111,3): error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.
src/store/slices/__tests__/messageSlice.selectors.test.ts(112,5): error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.
src/store/slices/__tests__/messageSlice.selectors.test.ts(115,7): error TS2304: Cannot find name 'expect'.
src/store/slices/__tests__/messageSlice.selectors.test.ts(116,7): error TS2304: Cannot find name 'expect'.
src/store/slices/__tests__/messageSlice.selectors.test.ts(119,5): error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.
src/store/slices/__tests__/messageSlice.selectors.test.ts(122,7): error TS2304: Cannot find name 'expect'.
src/store/slices/__tests__/messageSlice.selectors.test.ts(123,7): error TS2304: Cannot find name 'expect'.
src/store/slices/__tests__/messageSlice.selectors.test.ts(126,5): error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.
src/store/slices/__tests__/messageSlice.selectors.test.ts(129,7): error TS2304: Cannot find name 'expect'.
src/store/slices/__tests__/messageSlice.selectors.test.ts(130,7): error TS2304: Cannot find name 'expect'.
src/store/slices/__tests__/messageSlice.selectors.test.ts(134,3): error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.
src/store/slices/__tests__/messageSlice.selectors.test.ts(135,5): error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.
src/store/slices/__tests__/messageSlice.selectors.test.ts(161,7): error TS2304: Cannot find name 'expect'.
src/store/slices/__tests__/messageSlice.selectors.test.ts(162,7): error TS2304: Cannot find name 'expect'.
src/store/slices/__tests__/messageSlice.test.ts(14,3): error TS6133: 'clearMessages' is declared but its value is never read.
src/store/slices/__tests__/messageSlice.test.ts(15,3): error TS6133: 'setError' is declared but its value is never read.
src/store/slices/__tests__/messageSlice.test.ts(22,8): error TS6133: 'MessageStatusType' is declared but its value is never read.
src/store/slices/__tests__/messageSlice.test.ts(27,1): error TS2304: Cannot find name 'global'.
src/store/slices/__tests__/messageSlice.test.ts(40,7): error TS2322: Type 'null' is not assignable to type 'string | undefined'.
src/store/slices/__tests__/messageSlice.test.ts(70,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(79,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(94,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(97,55): error TS7006: Parameter 'msg' implicitly has an 'any' type.
src/store/slices/__tests__/messageSlice.test.ts(106,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(128,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(149,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(180,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(201,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(214,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(224,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(238,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(255,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(265,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(275,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(286,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(319,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(332,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(346,21): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(362,23): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(373,23): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(385,23): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(402,23): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(414,23): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(428,23): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(440,23): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(459,23): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(470,23): error TS2571: Object is of type 'unknown'.
src/store/slices/__tests__/messageSlice.test.ts(485,23): error TS2571: Object is of type 'unknown'.
src/store/slices/callingSlice.ts(368,46): error TS6133: 'action' is declared but its value is never read.
src/store/slices/encryptionSlice.ts(7,3): error TS6196: 'StoredIdentityKeys' is declared but never used.
src/store/slices/encryptionSlice.ts(8,3): error TS6196: 'StoredSignedPreKey' is declared but never used.
src/store/slices/encryptionSlice.ts(12,3): error TS6196: 'KeyBundleResponse' is declared but never used.
src/store/slices/encryptionSlice.ts(17,3): error TS6133: 'generateKeyBundle' is declared but its value is never read.
src/store/slices/encryptionSlice.ts(18,3): error TS6133: 'generateOneTimePreKeyBatch' is declared but its value is never read.
src/store/slices/encryptionSlice.ts(69,35): error TS6133: 'progress' is declared but its value is never read.
src/store/slices/encryptionSlice.ts(69,45): error TS6133: 'message' is declared but its value is never read.
src/store/slices/encryptionSlice.ts(308,62): error TS6133: 'action' is declared but its value is never read.
src/test/integration/messageStatus.integration.test.tsx(149,14): error TS18046: 'state' is of type 'unknown'.
src/test/integration/messageStatus.integration.test.tsx(167,14): error TS18046: 'state' is of type 'unknown'.
src/test/integration/messageStatus.integration.test.tsx(197,14): error TS18046: 'state' is of type 'unknown'.
src/test/integration/messageStatus.integration.test.tsx(357,16): error TS18046: 'state' is of type 'unknown'.
src/test/integration/messageStatus.integration.test.tsx(374,16): error TS18046: 'state' is of type 'unknown'.
src/test/integration/messageStatus.integration.test.tsx(403,16): error TS18046: 'state' is of type 'unknown'.
src/test/mocks/handlers.ts(138,35): error TS6133: 'request' is declared but its value is never read.
src/test/setup.ts(8,1): error TS2304: Cannot find name 'global'.
src/test/setup.ts(15,1): error TS2304: Cannot find name 'global'.
src/test/setup.ts(64,3): error TS2322: Type 'string' is not assignable to type '{ (regexp: string | RegExp): number; (searcher: { [Symbol.search](string: string): number; }): number; } & string'.
  Type 'string' is not assignable to type '{ (regexp: string | RegExp): number; (searcher: { [Symbol.search](string: string): number; }): number; }'.
src/test/utils.tsx(27,25): error TS2339: Property 'conversations' does not exist on type '{}'.
src/test/utils.tsx(36,25): error TS2339: Property 'messages' does not exist on type '{}'.
src/types/api.ts(182,3): error TS2314: Generic type 'MutationResult<T>' requires 1 type argument(s).
src/types/api.ts(187,3): error TS2314: Generic type 'MutationResult<T>' requires 1 type argument(s).
src/types/api.ts(192,3): error TS2314: Generic type 'MutationResult<T>' requires 1 type argument(s).
src/types/api.ts(208,3): error TS2314: Generic type 'MutationResult<T>' requires 1 type argument(s).
src/types/api.ts(219,3): error TS2314: Generic type 'MutationResult<T>' requires 1 type argument(s).
