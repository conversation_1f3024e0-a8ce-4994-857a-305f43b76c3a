// e2e/tests/conversation-creation-notification-fix.spec.ts
import { test, expect, Page, BrowserContext } from '@playwright/test';

// Test configuration
const FRONTEND_URL = 'http://localhost:5000';
const BACKEND_URL = 'http://localhost:6000';

// Test users
const ALICE = {
  email: '<EMAIL>',
  password: 'testpass123',
  username: 'alice'
};

const HARRY = {
  email: '<EMAIL>',
  password: 'testpass123',
  username: 'harry'
};

// Helper functions
async function loginUser(page: Page, email: string, password: string) {
  await page.goto(FRONTEND_URL);
  
  // Wait for login form to be visible
  await page.waitForSelector('form', { timeout: 10000 });
  
  // Fill login form
  await page.fill('[data-testid="email-input"]', email);
  await page.fill('[data-testid="password-input"]', password);
  
  // Submit login
  await page.click('[data-testid="login-button"]');
  
  // Wait for successful login (dashboard header should be visible)
  await page.waitForSelector('[data-testid="dashboard-header"]', { timeout: 15000 });
  
  // Wait for socket connection
  await page.waitForSelector('[data-testid="connection-status"]:has-text("Connected")', { timeout: 15000 });
}

async function cleanupConversations(page: Page) {
  // Navigate to conversations API to clean up any existing conversations
  try {
    await page.evaluate(async () => {
      const token = localStorage.getItem('token');
      if (token) {
        // Get all conversations
        const response = await fetch('/api/messaging/conversations/', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          const data = await response.json();
          console.log('🧹 Found conversations to clean up:', data.results?.length || 0);
          
          // Delete each conversation (if API supports it)
          for (const conversation of data.results || []) {
            try {
              await fetch(`/api/messaging/conversations/${conversation.id}/`, {
                method: 'DELETE',
                headers: {
                  'Authorization': `Bearer ${token}`,
                  'Content-Type': 'application/json'
                }
              });
              console.log('🗑️ Deleted conversation:', conversation.id);
            } catch (e) {
              console.log('⚠️ Could not delete conversation:', conversation.id, e);
            }
          }
        }
      }
    });
  } catch (error) {
    console.log('⚠️ Cleanup error (continuing anyway):', error);
  }
}

async function searchForUser(page: Page, username: string) {
  // Click new chat button to open user search modal
  await page.click('[data-testid="new-chat-button"]');
  
  // Wait for user search modal to appear
  await page.waitForSelector('[data-testid="user-search-modal"]', { timeout: 10000 });
  
  // Fill search input
  await page.fill('[data-testid="user-search-input"]', username);
  
  // Wait for search results
  await page.waitForTimeout(2000); // Allow search to complete
}

async function sendFirstMessage(page: Page, username: string, message: string) {
  await searchForUser(page, username);
  
  // Click on user action button to create chat
  await page.click('[data-testid="user-action-button"]');
  
  // Wait for chat area to be visible (conversation should be selected)
  await page.waitForSelector('[data-testid="chat-area"]', { timeout: 10000 });
  
  // Wait for message input
  await page.waitForSelector('[data-testid="message-input"]', { timeout: 5000 });
  
  // Type message
  await page.fill('[data-testid="message-input"]', message);
  
  // Send message
  await page.click('[data-testid="send-button"]');
  
  // Wait for message to appear in chat
  await page.waitForSelector(`[data-testid="message"]:has-text("${message}")`, { timeout: 10000 });
}

async function checkForConversationInList(page: Page, timeout: number = 5000): Promise<boolean> {
  try {
    await page.waitForSelector('[data-testid="conversation-list"] .hover\\:bg-gray-50', { timeout });
    const conversationCount = await page.locator('[data-testid="conversation-list"] .hover\\:bg-gray-50').count();
    return conversationCount > 0;
  } catch (error) {
    return false;
  }
}

// Test Suite
test.describe('Conversation Creation Notification Fix', () => {
  
  test('Fix conversation creation notification issue with iterative testing', async ({ browser }) => {
    console.log('🚀 Starting Conversation Creation Notification Fix Test');
    
    let attempt = 1;
    const maxAttempts = 5;
    let notificationWorking = false;
    
    while (attempt <= maxAttempts && !notificationWorking) {
      console.log(`\n🔄 ATTEMPT ${attempt}/${maxAttempts}: Testing conversation creation notification`);
      
      // Create two browser contexts for real-time testing
      const context1 = await browser.newContext();
      const context2 = await browser.newContext();
      
      const alicePage = await context1.newPage();
      const harryPage = await context2.newPage();
      
      // Set up monitoring for both pages
      [alicePage, harryPage].forEach((page, index) => {
        const userName = index === 0 ? 'Alice' : 'Harry';
        page.on('console', (msg) => {
          console.log(`${userName} Console ${msg.type()}: ${msg.text()}`);
        });
        
        page.on('pageerror', (error) => {
          console.error(`${userName} Page error: ${error.message}`);
        });
      });
      
      try {
        // Step 1: Login both users
        console.log('📝 Step 1: Login both users');
        await loginUser(alicePage, ALICE.email, ALICE.password);
        console.log('✅ Alice logged in successfully');
        
        await loginUser(harryPage, HARRY.email, HARRY.password);
        console.log('✅ Harry logged in successfully');
        
        // Step 2: Clean up any existing conversations
        console.log('📝 Step 2: Clean up existing conversations');
        await cleanupConversations(alicePage);
        await cleanupConversations(harryPage);
        
        // Refresh both pages to ensure clean state
        await alicePage.reload();
        await harryPage.reload();
        
        // Wait for reconnection
        await alicePage.waitForSelector('[data-testid="connection-status"]:has-text("Connected")', { timeout: 15000 });
        await harryPage.waitForSelector('[data-testid="connection-status"]:has-text("Connected")', { timeout: 15000 });
        
        console.log('✅ Cleanup completed, pages refreshed');
        
        // Step 3: Verify both users start with no conversations
        console.log('📝 Step 3: Verify clean state - no conversations');
        const aliceHasConversationsBefore = await checkForConversationInList(alicePage, 2000);
        const harryHasConversationsBefore = await checkForConversationInList(harryPage, 2000);
        
        console.log(`Alice has conversations before: ${aliceHasConversationsBefore}`);
        console.log(`Harry has conversations before: ${harryHasConversationsBefore}`);
        
        // Step 4: Alice sends first message to Harry (creates conversation)
        console.log('📝 Step 4: Alice sends first message to Harry');
        const testMessage = `Hello Harry! This is attempt ${attempt} to test conversation creation notification.`;
        await sendFirstMessage(alicePage, HARRY.username, testMessage);
        console.log('✅ Alice sent first message');
        
        // Step 5: Check if Harry receives conversation creation notification
        console.log('📝 Step 5: Check if Harry receives conversation creation notification');
        
        // Wait a bit for the notification to propagate
        await harryPage.waitForTimeout(3000);
        
        const harryHasConversationsAfter = await checkForConversationInList(harryPage, 5000);
        console.log(`Harry has conversations after Alice's message: ${harryHasConversationsAfter}`);
        
        if (harryHasConversationsAfter) {
          console.log('🎉 SUCCESS: Harry received conversation creation notification!');
          notificationWorking = true;
          
          // Verify Alice also has the conversation
          const aliceHasConversationsAfter = await checkForConversationInList(alicePage, 2000);
          console.log(`Alice has conversations after sending message: ${aliceHasConversationsAfter}`);
          
          if (aliceHasConversationsAfter) {
            console.log('🎉 PERFECT: Both users can see the conversation!');
          } else {
            console.log('⚠️ Alice cannot see the conversation she created');
          }
          
        } else {
          console.log('❌ FAILED: Harry did not receive conversation creation notification');
          console.log('🔍 Investigating the issue...');
          
          // Check if Alice can see the conversation she created
          const aliceHasConversationsAfter = await checkForConversationInList(alicePage, 2000);
          console.log(`Alice has conversations after sending message: ${aliceHasConversationsAfter}`);
          
          // Log socket connection status
          const aliceSocketStatus = await alicePage.textContent('[data-testid="connection-status"]');
          const harrySocketStatus = await harryPage.textContent('[data-testid="connection-status"]');
          console.log(`Alice socket status: ${aliceSocketStatus}`);
          console.log(`Harry socket status: ${harrySocketStatus}`);
          
          if (attempt === maxAttempts) {
            console.log('🔧 DIAGNOSIS: Conversation creation notification is not working properly');
            console.log('🔧 This likely indicates an issue with:');
            console.log('   1. Backend not sending conversation_created socket event');
            console.log('   2. Frontend not listening for conversation_created event');
            console.log('   3. Socket.IO room management for notifications');
            console.log('   4. Conversation creation API not triggering notifications');
          }
        }
        
      } finally {
        await context1.close();
        await context2.close();
      }
      
      if (!notificationWorking) {
        attempt++;
        if (attempt <= maxAttempts) {
          console.log(`⏳ Waiting 2 seconds before next attempt...`);
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    }
    
    // Final assertion
    expect(notificationWorking).toBe(true);
    console.log('🎉 Conversation creation notification test completed successfully!');
  });
});
