// Debug script to test socket notifications
const io = require('socket.io-client');

// Test user credentials (you may need to adjust these)
const ALICE_TOKEN = 'your-alice-token-here';
const HARRY_TOKEN = 'your-harry-token-here';
const ALICE_ID = '1';
const HARRY_ID = '2';

// Connect Alice (caller)
const aliceSocket = io('http://localhost:7000', {
  auth: { token: ALICE_TOKEN }
});

// Connect Harry (callee)
const harrySocket = io('http://localhost:7000', {
  auth: { token: HARRY_TOKEN }
});

aliceSocket.on('connect', () => {
  console.log('✅ Alice connected:', aliceSocket.id);
});

harrySocket.on('connect', () => {
  console.log('✅ Harry connected:', harrySocket.id);
  
  // Listen for incoming call on <PERSON>'s socket
  harrySocket.on('incoming_call', (data) => {
    console.log('🔔 <PERSON> received incoming_call:', data);
  });
  
  // After both are connected, simulate a call from <PERSON> to <PERSON>
  setTimeout(() => {
    console.log('📞 Alice initiating call to <PERSON>...');
    aliceSocket.emit('initiate_call', {
      callId: 'test-call-123',
      calleeId: HARRY_ID,
      callType: 'audio',
      conversationId: 'test-conv-123'
    });
  }, 1000);
});

aliceSocket.on('call_initiated', (data) => {
  console.log('✅ Alice received call_initiated:', data);
});

aliceSocket.on('call_ringing', (data) => {
  console.log('📱 Alice received call_ringing:', data);
});

// Error handlers
aliceSocket.on('call_error', (error) => {
  console.error('❌ Alice call error:', error);
});

harrySocket.on('call_error', (error) => {
  console.error('❌ Harry call error:', error);
});

// Disconnect after 10 seconds
setTimeout(() => {
  console.log('🔌 Disconnecting...');
  aliceSocket.disconnect();
  harrySocket.disconnect();
  process.exit(0);
}, 10000);