import { test, expect, Page, BrowserContext } from '@playwright/test';
import { TestDataManager } from '../utils/test-data-manager';
import { AuthHelper } from '../utils/auth-helper';

test.describe('Bidirectional Conversation Notifications', () => {
  let testDataManager: TestDataManager;
  let authHelper: AuthHelper;

  test.beforeAll(async () => {
    testDataManager = new TestDataManager();
    authHelper = new AuthHelper();
  });

  test('Direct message creation should notify recipient immediately', async ({ browser }) => {
    console.log('🚀 Testing Direct Message Creation Notifications');

    const aliceContext = await browser.newContext();
    const bobContext = await browser.newContext();
    
    const alicePage = await aliceContext.newPage();
    const bobPage = await bobContext.newPage();

    try {
      // Step 1: Login both users (using existing test users)
      console.log('👤 Logging in <PERSON> and <PERSON>...');
      await authHelper.loginUser(alicePage, '<EMAIL>', 'testpass123');
      await authHelper.loginUser(bobPage, '<EMAIL>', 'testpass123');

      // Step 2: Set up notification monitoring for Harry
      console.log('👂 Setting up notification monitoring for Harry...');
      await setupNotificationMonitoring(bobPage);

      // Step 3: Clean up existing conversations
      console.log('🧹 Cleaning up existing conversations...');
      await cleanupConversations(alicePage);
      await cleanupConversations(bobPage);

      // Step 4: Alice creates a direct conversation with Harry via API
      console.log('📝 Alice creating direct conversation with Harry...');
      const harryUserId = await getUserId(bobPage);
      const conversationId = await createDirectConversation(alicePage, harryUserId);

      // Step 5: Verify Harry receives notification
      console.log('⏳ Waiting for Harry to receive conversation creation notification...');
      await bobPage.waitForTimeout(3000);

      const notificationReceived = await bobPage.evaluate(() => {
        return (window as any).conversationNotificationReceived;
      });

      expect(notificationReceived).toBe(true);
      console.log('✅ SUCCESS: Harry received direct conversation creation notification!');

      // Step 6: Verify conversation appears in Harry's list
      await bobPage.reload();
      await bobPage.waitForSelector('[data-testid="conversation-list"]', { timeout: 10000 });

      const conversationVisible = await bobPage.locator('text=Alice Smith').isVisible();
      expect(conversationVisible).toBe(true);
      console.log('✅ VERIFIED: Conversation appears in Harry\'s conversation list');

    } finally {
      await aliceContext.close();
      await bobContext.close();
    }
  });

  test('Group conversation creation should notify all members immediately', async ({ browser }) => {
    console.log('🚀 Testing Group Conversation Creation Notifications');

    const aliceContext = await browser.newContext();
    const bobContext = await browser.newContext();
    const charlieContext = await browser.newContext();
    
    const alicePage = await aliceContext.newPage();
    const bobPage = await bobContext.newPage();
    const charliePage = await charlieContext.newPage();

    try {
      // Step 1: Login all users (using existing test users)
      console.log('👤 Logging in Alice, Harry, and Charlie...');
      await authHelper.loginUser(alicePage, '<EMAIL>', 'testpass123');
      await authHelper.loginUser(bobPage, '<EMAIL>', 'testpass123');
      await authHelper.loginUser(charliePage, '<EMAIL>', 'testpass123');

      // Step 2: Set up notification monitoring for Harry and Charlie
      console.log('👂 Setting up notification monitoring for Harry and Charlie...');
      await setupNotificationMonitoring(bobPage);
      await setupNotificationMonitoring(charliePage);

      // Step 3: Clean up existing conversations
      console.log('🧹 Cleaning up existing conversations...');
      await cleanupConversations(alicePage);
      await cleanupConversations(bobPage);
      await cleanupConversations(charliePage);

      // Step 4: Alice creates a group conversation with Harry and Charlie
      console.log('📝 Alice creating group conversation with Harry and Charlie...');
      const harryUserId = await getUserId(bobPage);
      const charlieUserId = await getUserId(charliePage);
      const groupId = await createGroupConversation(alicePage, [harryUserId, charlieUserId], 'Test Group Chat');

      // Step 5: Verify both Harry and Charlie receive notifications
      console.log('⏳ Waiting for Harry and Charlie to receive group creation notifications...');
      await Promise.all([
        bobPage.waitForTimeout(3000),
        charliePage.waitForTimeout(3000)
      ]);

      const [harryNotificationReceived, charlieNotificationReceived] = await Promise.all([
        bobPage.evaluate(() => (window as any).conversationNotificationReceived),
        charliePage.evaluate(() => (window as any).conversationNotificationReceived)
      ]);

      expect(harryNotificationReceived).toBe(true);
      expect(charlieNotificationReceived).toBe(true);
      console.log('✅ SUCCESS: Both Harry and Charlie received group creation notifications!');

      // Step 6: Verify group appears in both users' lists
      await Promise.all([
        bobPage.reload(),
        charliePage.reload()
      ]);

      await Promise.all([
        bobPage.waitForSelector('[data-testid="conversation-list"]', { timeout: 10000 }),
        charliePage.waitForSelector('[data-testid="conversation-list"]', { timeout: 10000 })
      ]);

      const [harryGroupVisible, charlieGroupVisible] = await Promise.all([
        bobPage.locator('text=Test Group Chat').isVisible(),
        charliePage.locator('text=Test Group Chat').isVisible()
      ]);

      expect(harryGroupVisible).toBe(true);
      expect(charlieGroupVisible).toBe(true);
      console.log('✅ VERIFIED: Group conversation appears in both Harry\'s and Charlie\'s conversation lists');

    } finally {
      await aliceContext.close();
      await bobContext.close();
      await charlieContext.close();
    }
  });

  // Helper functions
  async function setupNotificationMonitoring(page: Page): Promise<void> {
    await page.evaluate(() => {
      // Monitor for conversation_created events
      (window as any).conversationNotificationReceived = false;
      (window as any).conversationData = null;
      
      // Hook into the socket context to monitor events
      const originalConsoleLog = console.log;
      console.log = (...args) => {
        if (args[0] && args[0].includes('🔔 [CONVERSATION_CREATED]')) {
          (window as any).conversationNotificationReceived = true;
          if (args[0].includes('Full conversation data:')) {
            (window as any).conversationData = args[1];
          }
        }
        originalConsoleLog.apply(console, args);
      };
    });
  }

  async function cleanupConversations(page: Page): Promise<void> {
    try {
      await page.evaluate(async () => {
        const token = localStorage.getItem('token');
        if (token) {
          const response = await fetch('/api/messaging/conversations/', {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          
          if (response.ok) {
            const data = await response.json();
            for (const conversation of data.results || []) {
              try {
                await fetch(`/api/messaging/conversations/${conversation.id}/`, {
                  method: 'DELETE',
                  headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                  }
                });
              } catch (e) {
                console.log('Could not delete conversation:', conversation.id);
              }
            }
          }
        }
      });
    } catch (error) {
      console.log('Cleanup error (continuing anyway):', error);
    }
  }

  async function getUserId(page: Page): Promise<string> {
    return await page.evaluate(() => {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      return user.id;
    });
  }

  async function createDirectConversation(page: Page, participantId: string): Promise<string> {
    return await page.evaluate(async (participantId) => {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/messaging/conversations/create/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: 'DIRECT',
          participant_ids: [participantId]
        })
      });
      
      const data = await response.json();
      return data.id;
    }, participantId);
  }

  async function createGroupConversation(page: Page, memberIds: string[], groupName: string): Promise<string> {
    return await page.evaluate(async ({ memberIds, groupName }) => {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/messaging/groups/create/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: groupName,
          description: 'Test group for notification testing',
          member_ids: memberIds,
          is_public: false,
          max_participants: 50
        })
      });
      
      const data = await response.json();
      return data.id;
    }, { memberIds, groupName });
  }
});
