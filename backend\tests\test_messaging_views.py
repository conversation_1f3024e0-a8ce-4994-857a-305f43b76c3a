# backend/tests/test_messaging_views.py
import pytest
import json
import base64
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from unittest.mock import patch, MagicMock

from messaging.models import Conversation, ConversationParticipant, Message
from tests.factories import UserFactory, ConversationFactory, ConversationParticipantFactory, MessageFactory

User = get_user_model()


@pytest.mark.django_db
class TestMessagingViews:
    """Test messaging endpoints"""

    def setup_method(self):
        """Set up test data"""
        self.client = APIClient()
        self.user = UserFactory()
        self.other_user = UserFactory()
        
        # Authenticate client
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

    def test_list_conversations_success(self):
        """Test successful conversation listing"""
        # Create a conversation with the user as participant
        conversation = ConversationFactory()
        ConversationParticipantFactory(conversation=conversation, user=self.user)
        ConversationParticipantFactory(conversation=conversation, user=self.other_user)
        
        url = reverse('conversation-list')
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'results' in response.data
        assert len(response.data['results']) == 1
        assert str(response.data['results'][0]['id']) == str(conversation.id)

    def test_list_conversations_unauthenticated(self):
        """Test conversation listing without authentication"""
        self.client.credentials()  # Remove authentication
        
        url = reverse('conversation-list')
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_list_conversations_empty(self):
        """Test conversation listing when user has no conversations"""
        url = reverse('conversation-list')
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'results' in response.data
        assert len(response.data['results']) == 0

    @patch('utils.socket_service.SocketService')
    def test_create_conversation_success(self, mock_socket_service):
        """Test successful conversation creation"""
        mock_socket_service.return_value.emit_to_user = MagicMock()
        
        data = {
            'type': 'DIRECT',
            'participant_ids': [str(self.other_user.id)]
        }
        
        url = reverse('create-conversation')
        response = self.client.post(url, data, format='json')
        
        if response.status_code != status.HTTP_201_CREATED:
            print(f"Response status: {response.status_code}")
            print(f"Response data: {response.data}")
        
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['type'] == 'DIRECT'
        assert len(response.data['participants']) == 2
        
        # Verify conversation was created in database
        conversation = Conversation.objects.get(id=response.data['id'])
        assert conversation.type == 'DIRECT'
        assert conversation.participants.count() == 2

    @patch('utils.socket_service.SocketService')
    def test_create_conversation_existing_direct(self, mock_socket_service):
        """Test creating conversation when direct conversation already exists"""
        mock_socket_service.return_value.emit_to_user = MagicMock()
        
        # Create existing conversation
        existing_conversation = ConversationFactory(type='DIRECT')
        ConversationParticipantFactory(conversation=existing_conversation, user=self.user)
        ConversationParticipantFactory(conversation=existing_conversation, user=self.other_user)
        
        data = {
            'type': 'DIRECT',
            'participant_ids': [str(self.other_user.id)]
        }
        
        url = reverse('create-conversation')
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert str(response.data['id']) == str(existing_conversation.id)

    def test_create_conversation_invalid_participant(self):
        """Test conversation creation with invalid participant ID"""
        data = {
            'type': 'DIRECT',
            'participant_ids': ['00000000-0000-0000-0000-000000000000']  # Non-existent ID
        }
        
        url = reverse('create-conversation')
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'One or more participant IDs are invalid' in response.data['error']

    def test_create_conversation_invalid_data(self):
        """Test conversation creation with invalid data"""
        data = {
            'type': 'INVALID_TYPE',
            'participant_ids': []
        }
        
        url = reverse('create-conversation')
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'errors' in response.data

    def test_get_conversation_messages_success(self):
        """Test successful message retrieval"""
        conversation = ConversationFactory()
        ConversationParticipantFactory(conversation=conversation, user=self.user)
        message = MessageFactory(conversation=conversation, sender=self.user)
        
        url = reverse('conversation-messages', kwargs={'conversation_id': conversation.id})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'results' in response.data
        assert len(response.data['results']) == 1
        assert str(response.data['results'][0]['id']) == str(message.id)

    def test_get_conversation_messages_not_participant(self):
        """Test message retrieval when user is not a participant"""
        conversation = ConversationFactory()
        # Don't add user as participant
        
        url = reverse('conversation-messages', kwargs={'conversation_id': conversation.id})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert 'Not a participant in this conversation' in response.data['error']

    def test_get_conversation_messages_not_found(self):
        """Test message retrieval for non-existent conversation"""
        url = reverse('conversation-messages', kwargs={'conversation_id': '00000000-0000-0000-0000-000000000000'})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_send_message_success(self):
        """Test successful message sending"""
        conversation = ConversationFactory()
        ConversationParticipantFactory(conversation=conversation, user=self.user)
        
        data = {
            'content': 'Hello, world!',
            'message_type': 'TEXT'
        }
        
        url = reverse('send-message', kwargs={'conversation_id': conversation.id})
        response = self.client.post(url, data, format='json')
        
        # Debug output
        if response.status_code != 201:
            print(f"Response status: {response.status_code}")
            print(f"Response data: {response.data}")
        
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['content'] == 'Hello, world!'
        assert response.data['messageType'] == 'TEXT'
        assert str(response.data['sender']['id']) == str(self.user.id)
        
        # Verify message was created in database
        message = Message.objects.get(id=response.data['id'])
        assert message.content == 'Hello, world!'
        assert message.sender == self.user

    def test_send_message_encrypted(self):
        """Test sending encrypted message"""
        conversation = ConversationFactory()
        ConversationParticipantFactory(conversation=conversation, user=self.user)
        
        data = {
            'encrypted_content': base64.b64encode(b'encrypted_data_here').decode('utf-8'),
            'iv': base64.b64encode(b'init_vector1').decode('utf-8'),  # Exactly 12 bytes
            'sender_ratchet_key': base64.b64encode(b'ratchet_key_data').decode('utf-8'),
            'message_number': 1,
            'previous_chain_length': 0,
            'message_type': 'TEXT'
        }
        
        url = reverse('send-message', kwargs={'conversation_id': conversation.id})
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['is_encrypted'] is True
        assert response.data['encrypted_content'] == base64.b64encode(b'encrypted_data_here').decode('utf-8')
        assert 'content' not in response.data or response.data['content'] is None

    def test_send_message_not_participant(self):
        """Test message sending when user is not a participant"""
        conversation = ConversationFactory()
        # Don't add user as participant
        
        data = {
            'content': 'Hello, world!',
            'message_type': 'TEXT'
        }
        
        url = reverse('send-message', kwargs={'conversation_id': conversation.id})
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert 'Not a participant in this conversation' in response.data['error']

    def test_send_message_invalid_data(self):
        """Test message sending with invalid data"""
        conversation = ConversationFactory()
        ConversationParticipantFactory(conversation=conversation, user=self.user)
        
        data = {
            'content': 'x' * 5000,  # Too long
            'message_type': 'INVALID_TYPE'
        }
        
        url = reverse('send-message', kwargs={'conversation_id': conversation.id})
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'errors' in response.data

    def test_get_conversation_encryption_status(self):
        """Test getting conversation encryption status"""
        conversation = ConversationFactory()
        ConversationParticipantFactory(conversation=conversation, user=self.user)
        
        url = reverse('conversation-encryption-status', kwargs={'conversation_id': conversation.id})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'conversation_id' in response.data
        assert 'is_encrypted' in response.data
        assert 'participants' in response.data

    def test_search_users_success(self):
        """Test successful user search"""
        # Create some users to search
        UserFactory(username='john_doe', first_name='John', last_name='Doe')
        UserFactory(username='jane_smith', first_name='Jane', last_name='Smith')
        
        url = reverse('search-users')
        response = self.client.get(url, {'q': 'john'})
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True
        assert len(response.data['results']) == 1
        assert response.data['results'][0]['username'] == 'john_doe'

    def test_search_users_empty_query(self):
        """Test user search with empty query"""
        url = reverse('search-users')
        response = self.client.get(url, {'q': ''})
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True
        assert len(response.data['results']) == 0

    def test_search_users_short_query(self):
        """Test user search with query too short"""
        url = reverse('search-users')
        response = self.client.get(url, {'q': 'a'})
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data['success'] is False
        assert 'at least 2 characters' in response.data['error']

    def test_search_users_excludes_current_user(self):
        """Test that user search excludes the current user"""
        url = reverse('search-users')
        response = self.client.get(url, {'q': self.user.username})
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True
        # Should not find the current user
        user_ids = [result['id'] for result in response.data['results']]
        assert str(self.user.id) not in user_ids

    def test_get_user_profile_success(self):
        """Test successful user profile retrieval"""
        target_user = UserFactory()
        
        url = reverse('get-user-profile', kwargs={'user_id': target_user.id})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True
        assert response.data['data']['id'] == str(target_user.id)
        assert response.data['data']['username'] == target_user.username

    def test_get_user_profile_not_found(self):
        """Test user profile retrieval for non-existent user"""
        url = reverse('get-user-profile', kwargs={'user_id': '00000000-0000-0000-0000-000000000000'})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.data['success'] is False
        assert 'User not found' in response.data['error']

    def test_pagination(self):
        """Test pagination for conversations and messages"""
        # Create multiple conversations
        for _ in range(15):
            conversation = ConversationFactory()
            ConversationParticipantFactory(conversation=conversation, user=self.user)
        
        url = reverse('conversation-list')
        response = self.client.get(url, {'page_size': 10})
        
        assert response.status_code == status.HTTP_200_OK
        assert 'next' in response.data
        assert 'previous' in response.data
        assert len(response.data['results']) == 10

    @patch('utils.socket_service.SocketService')
    def test_socket_service_error_handling(self, mock_socket_service):
        """Test that socket service errors don't break conversation creation"""
        mock_socket_service.return_value.emit_to_user.side_effect = Exception("Socket error")
        
        data = {
            'type': 'DIRECT',
            'participant_ids': [str(self.other_user.id)]
        }
        
        url = reverse('create-conversation')
        response = self.client.post(url, data, format='json')
        
        # Should still succeed despite socket error
        assert response.status_code == status.HTTP_201_CREATED
