Expected User Flow Now:
Audio/Video Call Initiation:
✅ Caller clicks call button → Immediately shows ActiveCallInterface with "Initiating..." status
✅ API call is made → Status updates to "Calling..." when backend confirms
✅ Callee receives incoming call → Shows IncomingCallModal
✅ Callee clicks Answer → Immediately hides modal and shows ActiveCallInterface
✅ Both parties see ActiveCallInterface with "Connected" status
✅ WebRTC connection can be established between both parties
Call Decline Flow:
✅ Callee clicks Decline → Immediately hides IncomingCallModal
✅ Caller's ActiveCallInterface is hidden via socket event
✅ Both parties return to normal chat interface