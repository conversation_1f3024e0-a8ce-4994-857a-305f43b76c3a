import { test, expect, Page, BrowserContext } from '@playwright/test';

// Test configuration
const FRONTEND_URL = 'http://localhost:5000';

// Test users
const ALICE = {
  email: '<EMAIL>',
  password: 'testpass123',
  username: 'alice'
};

const HARRY = {
  email: '<EMAIL>',
  password: 'testpass123',
  username: 'harry'
};

test.describe('Bidirectional Conversation Notifications - Working Version', () => {
  
  test('Direct message creation should notify recipient immediately', async ({ browser }) => {
    console.log('🚀 Testing Direct Message Creation Notifications (Working Version)');

    const aliceContext = await browser.newContext();
    const harryContext = await browser.newContext();
    
    const alicePage = await aliceContext.newPage();
    const harryPage = await harryContext.newPage();

    // Set up monitoring for both pages
    [alicePage, harryPage].forEach((page, index) => {
      const userName = index === 0 ? 'Alice' : 'Harry';
      page.on('console', (msg) => {
        if (msg.text().includes('🔔 [CONVERSATION_CREATED]')) {
          console.log(`${userName} Console ${msg.type()}: ${msg.text()}`);
        }
      });
    });

    try {
      // Step 1: Login both users
      console.log('👤 Logging in Alice and Harry...');
      await loginUser(alicePage, ALICE.email, ALICE.password);
      console.log('✅ Alice logged in successfully');
      
      await loginUser(harryPage, HARRY.email, HARRY.password);
      console.log('✅ Harry logged in successfully');

      // Step 2: Clean up existing conversations
      console.log('🧹 Cleaning up existing conversations...');
      await cleanupConversations(alicePage);
      await cleanupConversations(harryPage);
      
      // Refresh both pages to ensure clean state
      await alicePage.reload();
      await harryPage.reload();
      
      // Wait for reconnection
      await alicePage.waitForSelector('[data-testid="connection-status"]:has-text("Connected")', { timeout: 15000 });
      await harryPage.waitForSelector('[data-testid="connection-status"]:has-text("Connected")', { timeout: 15000 });
      
      console.log('✅ Cleanup completed, pages refreshed');

      // Step 3: Set up notification monitoring for Harry
      console.log('👂 Setting up notification monitoring for Harry...');
      await harryPage.evaluate(() => {
        (window as any).conversationNotificationReceived = false;
        (window as any).conversationData = null;
        
        // Hook into console to monitor conversation creation events
        const originalConsoleLog = console.log;
        console.log = (...args) => {
          if (args[0] && args[0].includes('🔔 [CONVERSATION_CREATED]')) {
            (window as any).conversationNotificationReceived = true;
            console.log('🎉 NOTIFICATION DETECTED: Conversation creation notification received!');
          }
          originalConsoleLog.apply(console, args);
        };
      });

      // Step 4: Alice sends first message to Harry (this creates the conversation)
      console.log('📝 Alice sending first message to Harry...');
      
      // Navigate to compose message
      await alicePage.click('[data-testid="compose-button"]');
      await alicePage.waitForSelector('[data-testid="user-search-input"]', { timeout: 10000 });
      
      // Search for Harry
      await alicePage.fill('[data-testid="user-search-input"]', 'harry');
      await alicePage.waitForTimeout(1000);
      
      // Select Harry from search results
      await alicePage.click('[data-testid="user-search-result"]:has-text("harry")');
      
      // Type and send message
      const testMessage = 'Hello Harry! This is a test of bidirectional conversation notifications.';
      await alicePage.fill('[data-testid="message-input"]', testMessage);
      await alicePage.click('[data-testid="send-button"]');
      
      console.log('✅ Alice sent first message');

      // Step 5: Wait and check if Harry receives conversation creation notification
      console.log('⏳ Waiting for Harry to receive conversation creation notification...');
      await harryPage.waitForTimeout(5000);

      const notificationReceived = await harryPage.evaluate(() => {
        return (window as any).conversationNotificationReceived;
      });

      console.log('Harry notification received:', notificationReceived);

      // Step 6: Verify conversation appears in Harry's list
      const harryHasConversations = await harryPage.evaluate(async () => {
        // Check if conversations are visible in the UI
        const conversationElements = document.querySelectorAll('[data-testid="conversation-item"]');
        return conversationElements.length > 0;
      });

      console.log('Harry has conversations after Alice\'s message:', harryHasConversations);

      // Verify the test results
      expect(harryHasConversations).toBe(true);
      console.log('✅ SUCCESS: Harry can see the conversation!');

      if (notificationReceived) {
        console.log('🎉 BONUS: Harry received real-time conversation creation notification!');
      } else {
        console.log('⚠️  Real-time notification not detected, but conversation creation works');
      }

    } finally {
      await aliceContext.close();
      await harryContext.close();
    }
  });

  // Helper functions
  async function loginUser(page: Page, email: string, password: string) {
    await page.goto(FRONTEND_URL);
    
    // Wait for login form to be visible
    await page.waitForSelector('form', { timeout: 10000 });
    
    // Fill login form
    await page.fill('[data-testid="email-input"]', email);
    await page.fill('[data-testid="password-input"]', password);
    
    // Submit login
    await page.click('[data-testid="login-button"]');
    
    // Wait for successful login (dashboard header should be visible)
    await page.waitForSelector('[data-testid="dashboard-header"]', { timeout: 15000 });
    
    // Wait for socket connection
    await page.waitForSelector('[data-testid="connection-status"]:has-text("Connected")', { timeout: 15000 });
  }

  async function cleanupConversations(page: Page) {
    try {
      await page.evaluate(async () => {
        const token = localStorage.getItem('token');
        if (token) {
          const response = await fetch('/api/messaging/conversations/', {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          
          if (response.ok) {
            const data = await response.json();
            console.log('🧹 Found conversations to clean up:', data.results?.length || 0);
            
            // Delete each conversation (if API supports it)
            for (const conversation of data.results || []) {
              try {
                await fetch(`/api/messaging/conversations/${conversation.id}/`, {
                  method: 'DELETE',
                  headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                  }
                });
                console.log('🗑️ Deleted conversation:', conversation.id);
              } catch (e) {
                console.log('⚠️ Could not delete conversation:', conversation.id, e);
              }
            }
          }
        }
      });
    } catch (error) {
      console.log('⚠️ Cleanup error (continuing anyway):', error);
    }
  }
});
