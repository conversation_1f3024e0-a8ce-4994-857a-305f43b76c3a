// socket-server/src/server.ts
import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import { PrismaClient } from '@prisma/client';
import { authenticateSocket, AuthenticatedSocket } from './middleware/auth';
import { MessageService } from './services/messageService';
import { ConversationService } from './services/conversationService';
import { MessageStatusService } from './services/messageStatusService';
import { EncryptionService } from './services/encryptionService';
import { CallingService } from './services/callingService';
import { SocketEventHandler } from './events/socketEvents';
import { CallingEventHandler } from './events/callingEvents';
import { registerDebugEvents } from './events/debugEvents';

const app = express();
const server = createServer(app);
const prisma = new PrismaClient();

// Initialize services
const messageService = new MessageService(prisma);
const conversationService = new ConversationService(prisma);
const messageStatusService = new MessageStatusService(prisma);
const encryptionService = new EncryptionService(prisma);
const callingService = new CallingService(prisma);

// CORS configuration
app.use(cors({
  origin: ['http://localhost:5000', 'http://127.0.0.1:5000', 'http://localhost:5001', 'http://127.0.0.1:5001', 'http://localhost:5002', 'http://127.0.0.1:5002', 'http://localhost:6000', 'http://127.0.0.1:6000', 'http://localhost:8000', 'http://127.0.0.1:8000'],
  credentials: true,
}));

// Middleware for parsing JSON
app.use(express.json());

// API endpoint to handle conversation creation notifications
app.post('/api/notify-conversation-created', (req, res) => {
  try {
    const { event, conversation, participant_ids, creator_id } = req.body;

    if (event === 'conversation_created' && conversation && participant_ids) {
      // Check connected sockets
      const connectedSockets = Array.from(io.sockets.sockets.keys());

      // Notify all participants about the new conversation
      participant_ids.forEach((participantId: string) => {
        const userRoom = `user:${participantId}`;

        // Check if there are any sockets in this room
        const socketsInRoom = io.sockets.adapter.rooms.get(userRoom);

        io.to(userRoom).emit('conversation_created', {
          conversation,
          creator_id,
          timestamp: new Date().toISOString()
        });
      });

      console.log(`Conversation ${conversation.id} creation notification sent to ${participant_ids.length} participants`);
      res.json({ success: true, notified: participant_ids.length });
    } else {
      res.status(400).json({ error: 'Invalid notification data' });
    }
  } catch (error) {
    console.error('Error sending conversation creation notification:', error);
    res.status(500).json({ error: 'Failed to send notification' });
  }
});

// Socket.io server setup
const io = new Server(server, {
  cors: {
    origin: ['http://localhost:5000', 'http://127.0.0.1:5000', 'http://localhost:5001', 'http://127.0.0.1:5001', 'http://localhost:5002', 'http://127.0.0.1:5002', 'http://localhost:6000', 'http://127.0.0.1:6000'],
    methods: ['GET', 'POST'],
    credentials: true,
  },
});

// HTTP endpoint for Django backend to emit socket events
app.post('/api/emit', (req, res) => {
  try {
    const { event, data, room } = req.body;

    if (!event || !data) {
      return res.status(400).json({ error: 'Missing event or data' });
    }

    console.log(`HTTP emit: ${event} to ${room || 'all'} with data:`, data);

    // Handle media_upload_completed specially to trigger proper event flow
    if (event === 'media_upload_completed') {
      // Trigger the Socket.IO event handler to process the media message
      // This will emit media_message_received and new_message events
      socketEventHandler.handleMediaUploadCompletedFromHttp(data, room);
    } else {
      // For other events, emit directly
      if (room) {
        io.to(room).emit(event, data);
      } else {
        io.emit(event, data);
      }
    }

    res.json({ success: true, message: 'Event emitted successfully' });
  } catch (error) {
    console.error('Error in /api/emit:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Initialize socket event handlers
const socketEventHandler = new SocketEventHandler(io, messageService, conversationService, messageStatusService, encryptionService);
const callingEventHandler = new CallingEventHandler(io, callingService);

// Authentication middleware
io.use(authenticateSocket);

// Socket connection handler
io.on('connection', (socket) => {
  const authSocket = socket as AuthenticatedSocket;
  console.log(`User ${authSocket.user.username} connected`);

  // Join user to their personal room
  authSocket.join(`user:${authSocket.userId}`);

  // Initialize message handlers using the service layer
  socketEventHandler.handleConnection(authSocket);

  // Initialize calling handlers
  callingEventHandler.handleConnection(authSocket);
  
  // Initialize debug event handlers
  registerDebugEvents(io, authSocket);
  
  // Log when user connects for debugging
  console.log(`Debug events registered for user ${authSocket.userId}`);
});

const PORT = process.env.PORT || 7000;

server.listen(PORT, () => {
  console.log(`Socket server running on port ${PORT}`);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  await prisma.$disconnect();
  server.close();
});

process.on('SIGINT', async () => {
  await prisma.$disconnect();
  server.close();
});


