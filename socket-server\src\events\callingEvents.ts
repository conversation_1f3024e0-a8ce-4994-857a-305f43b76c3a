// socket-server/src/events/callingEvents.ts
import { Server, Socket } from 'socket.io';
import { CallingService } from '../services/callingService';
import {
  InitiateCallSchema,
  CallResponseSchema,
  WebRTCOfferSchema,
  WebRTCAnswerSchema,
  WebRTCIceCandidateSchema,
  MediaToggleSchema,
  CallQualityReportSchema,
  validateSocketEvent,
  createSocketError,
  type InitiateCall,
  type CallResponse,
  type WebRTCOffer,
  type WebRTCAnswer,
  type WebRTCIceCandidate,
  type MediaToggle,
  type CallQualityReport,
} from '../schemas/callingSchemas';

interface AuthenticatedSocket extends Socket {
  userId: string;
  user: {
    id: string;
    username: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
  };
}

export class CallingEventHandler {
  private activeCalls = new Map<string, {
    callId: string;
    callerId: string;
    calleeId: string;
    callType: 'audio' | 'video';
    status: string;
    callerSocketId?: string;
    calleeSocketId?: string;
  }>();

  constructor(
    private io: Server,
    private callingService: CallingService
  ) {}

  handleConnection(socket: AuthenticatedSocket) {
    console.log(`User ${socket.userId} connected to calling service`);

    // Handle call initiation
    socket.on('initiate_call', (data) => this.handleInitiateCall(socket, data));

    // Handle call responses
    socket.on('answer_call', (data) => this.handleAnswerCall(socket, data));
    socket.on('decline_call', (data) => this.handleDeclineCall(socket, data));
    socket.on('end_call', (data) => this.handleEndCall(socket, data));
    
    // Debug endpoint to check room members
      socket.on('debug_room_members', async (data: { room: string }, callback: Function) => {
        try {
          const { room } = data;
          const roomMembers = this.io.sockets.adapter.rooms.get(room);
          const members = roomMembers ? Array.from(roomMembers) : [];
          callback({ success: true, room, members });
          console.log(`🔍 Debug: Room ${room} members:`, members);
        } catch (error) {
          console.error('Error getting room members:', error);
          callback({ success: false, error: error instanceof Error ? error.message : 'Unknown error occurred' });
        }
      });
      
      // Debug endpoint to manually emit call_active event
      socket.on('debug_emit_call_active', async (data: { callId: string }) => {
        try {
          const { callId } = data;
          const callRoom = `call:${callId}`;
          
          console.log(`🔍 Debug: Manually emitting call_active to room ${callRoom}`);
          
          // Log room members before emitting
          const roomMembers = this.io.sockets.adapter.rooms.get(callRoom);
          const members = roomMembers ? Array.from(roomMembers) : [];
          console.log(`🔍 Debug: Room ${callRoom} members before manual call_active:`, members);
          
          // Emit call_active event to the room
          this.io.to(callRoom).emit('call_active', { callId });
          
          console.log(`🔍 Debug: Manual call_active event emitted to room ${callRoom}`);
        } catch (error) {
          console.error('Error manually emitting call_active:', error);
        }
      });

    // Handle WebRTC signaling
    socket.on('webrtc_offer', (data) => this.handleWebRTCOffer(socket, data));
    socket.on('webrtc_answer', (data) => this.handleWebRTCAnswer(socket, data));
    socket.on('webrtc_ice_candidate', (data) => this.handleWebRTCIceCandidate(socket, data));

    // Handle media controls
    socket.on('toggle_audio', (data) => this.handleMediaToggle(socket, { ...data, mediaType: 'audio' }));
    socket.on('toggle_video', (data) => this.handleMediaToggle(socket, { ...data, mediaType: 'video' }));

    // Handle call quality reports
    socket.on('call_quality_report', (data) => this.handleCallQualityReport(socket, data));

    // Handle disconnection
    socket.on('disconnect', () => this.handleDisconnection(socket));
  }

  private async handleInitiateCall(socket: AuthenticatedSocket, data: unknown) {
    console.log('🔥 handleInitiateCall called by user:', socket.userId, 'with data:', JSON.stringify(data, null, 2));
    try {
      console.log('🔍 Validating data against InitiateCallSchema...');
      const validatedData = validateSocketEvent(InitiateCallSchema, data);
      console.log('✅ Data validation successful:', JSON.stringify(validatedData, null, 2));
      console.log('✅ Data validated:', validatedData);
      
      // Check if there's already an active call for this conversation
      const existingCall = Array.from(this.activeCalls.values()).find(
        call => call.callerId === socket.userId || call.calleeId === socket.userId
      );

      if (existingCall) {
        socket.emit('call_error', createSocketError('User already in a call'));
        return;
      }

      // Generate session ID
      const sessionId = `call_${validatedData.conversationId}_${Date.now()}`;

      // Get the callee from the conversation participants
      const conversation = await this.callingService.getConversationWithParticipants(validatedData.conversationId);
      if (!conversation) {
        socket.emit('call_error', createSocketError('Conversation not found'));
        return;
      }

      // Find the other participant (callee)
      const callee = conversation.participants.find(p => p.userId !== socket.userId);
      if (!callee) {
        socket.emit('call_error', createSocketError('No other participant found in conversation'));
        return;
      }

      // Create call in database
      const call = await this.callingService.createCall({
        conversationId: validatedData.conversationId,
        callerId: socket.userId,
        calleeId: callee.userId,
        callType: validatedData.callType,
        sessionId,
      });

      // Store call session
      const callSession = {
        callId: call.id,
        callerId: socket.userId,
        calleeId: call.calleeId,
        callType: validatedData.callType,
        status: 'initiated',
        callerSocketId: socket.id,
      };

      this.activeCalls.set(call.id, callSession);
      console.log(`📞 Stored call session:`, callSession);
      console.log(`📞 Active calls count:`, this.activeCalls.size);

      // Caller joins the call room immediately for WebRTC signaling
      socket.join(`call:${call.id}`);
      console.log(`📞 Caller ${socket.userId} joined call room: call:${call.id}`);

      // Create call event
      await this.callingService.createCallEvent({
        callId: call.id,
        eventType: 'call_initiated',
        userId: socket.userId,
        eventData: { callType: validatedData.callType },
      });

      // Notify callee
      this.io.to(`user:${call.calleeId}`).emit('incoming_call', {
        callId: call.id,
        caller: {
          id: socket.user.id,
          username: socket.user.username,
          firstName: socket.user.firstName,
          lastName: socket.user.lastName,
          profilePicture: socket.user.profilePicture,
        },
        callType: validatedData.callType,
        conversationId: validatedData.conversationId,
        timestamp: call.initiatedAt.toISOString(),
      });

      // Update call status to ringing after a delay
      setTimeout(async () => {
        const activeCall = this.activeCalls.get(call.id);
        if (activeCall && activeCall.status === 'initiated') {
          activeCall.status = 'ringing';
          await this.callingService.updateCallStatus(call.id, 'ringing');
          this.io.to(`user:${call.callerId}`).emit('call_ringing', { callId: call.id });
        }
      }, 1000);

      // Respond to caller
      socket.emit('call_initiated', { callId: call.id });

    } catch (error) {
      console.error('Error initiating call:', error);
      socket.emit('call_error', createSocketError(
        error instanceof Error ? error.message : 'Failed to initiate call'
      ));
    }
  }

  private async handleAnswerCall(socket: AuthenticatedSocket, data: unknown) {
    try {
      console.log('🔥 handleAnswerCall called by user:', socket.userId, 'with data:', data);
      const validatedData = validateSocketEvent(CallResponseSchema, data);
      console.log('✅ Answer call data validated:', validatedData);

      // Debug: Log all active calls
      console.log('📞 Active calls:', Array.from(this.activeCalls.entries()));

      const call = this.activeCalls.get(validatedData.callId);
      console.log('📞 Found call:', call);

      if (!call) {
        console.error('❌ Call not found in activeCalls for callId:', validatedData.callId);

        // Check if call exists in database
        try {
          const dbCall = await this.callingService.getCall(validatedData.callId);
          if (dbCall) {
            console.log('📞 Call exists in database but not in activeCalls, recreating session...');
            // Recreate the call session in activeCalls
            this.activeCalls.set(validatedData.callId, {
              callId: validatedData.callId,
              callerId: dbCall.callerId,
              calleeId: dbCall.calleeId,
              callType: dbCall.callType,
              status: dbCall.status,
              callerSocketId: '', // We don't have the caller socket ID, will be updated when needed
            });

            // Continue with the answer process
            const recreatedCall = this.activeCalls.get(validatedData.callId)!;
            console.log('📞 Recreated call session:', recreatedCall);
          } else {
            console.error('❌ Call not found in database either');
            socket.emit('call_error', createSocketError('Call not found'));
            return;
          }
        } catch (dbError) {
          console.error('❌ Error checking database for call:', dbError);
          socket.emit('call_error', createSocketError('Call not found'));
          return;
        }
      }

      // Get the call (either original or recreated)
      const currentCall = this.activeCalls.get(validatedData.callId)!;

      if (currentCall.calleeId !== socket.userId) {
        console.error('❌ Unauthorized: call.calleeId:', currentCall.calleeId, 'socket.userId:', socket.userId);
        socket.emit('call_error', createSocketError('Unauthorized to answer this call'));
        return;
      }

      // Update call status
      currentCall.status = 'answered';
      currentCall.calleeSocketId = socket.id;

      await this.callingService.updateCallStatus(validatedData.callId, 'answered', {
        answeredAt: new Date(),
      });

      // Create call event
      await this.callingService.createCallEvent({
        callId: validatedData.callId,
        eventType: 'call_answered',
        userId: socket.userId,
      });

      // Notify caller
      if (currentCall.callerSocketId) {
        this.io.to(currentCall.callerSocketId).emit('call_answered', {
          callId: validatedData.callId,
          calleeId: socket.userId,
          timestamp: new Date().toISOString(),
        });
      }

      // Both participants join the call room
      socket.join(`call:${validatedData.callId}`);
      console.log(`📞 Callee ${socket.userId} joined call room: call:${validatedData.callId}`);
      
      // Ensure caller is in the room too - find all active sockets for the caller
      const callerSockets = await this.io.in(`user:${currentCall.callerId}`).fetchSockets();
      console.log(`📞 Found ${callerSockets.length} sockets for caller ${currentCall.callerId}`);
      
      // Add all caller sockets to the call room and update the caller socket ID
      for (const callerSocket of callerSockets) {
        callerSocket.join(`call:${validatedData.callId}`);
        console.log(`📞 Added caller socket ${callerSocket.id} to call room: call:${validatedData.callId}`);
        // Update with the most recent active caller socket ID
        currentCall.callerSocketId = callerSocket.id;
      }
      
      // If no active caller sockets found, log warning but continue
      if (callerSockets.length === 0) {
        console.warn(`⚠️ No active sockets found for caller ${currentCall.callerId}`);
      }

      // Log room members after joining
      const roomMembers = this.io.sockets.adapter.rooms.get(`call:${validatedData.callId}`);
      console.log(`📞 Call room call:${validatedData.callId} members after answer:`, roomMembers ? Array.from(roomMembers) : 'none');

      // Check if we already have caller SDP data, if so, set call to active immediately
      const callData = await this.callingService.getCall(validatedData.callId);
      if (callData && callData.callerSdp) {
        console.log(`📞 Call ${validatedData.callId} already has caller SDP, setting to active immediately`);
        await this.callingService.updateCallStatus(validatedData.callId, 'active');
        
        // Emit call_active event to both participants
        this.io.to(`call:${validatedData.callId}`).emit('call_active', {
          callId: validatedData.callId,
          timestamp: new Date().toISOString()
        });
      }

      socket.emit('call_answered_success', { callId: validatedData.callId });

    } catch (error) {
      console.error('Error answering call:', error);
      socket.emit('call_error', createSocketError(
        error instanceof Error ? error.message : 'Failed to answer call'
      ));
    }
  }

  private async handleDeclineCall(socket: AuthenticatedSocket, data: unknown) {
    try {
      const validatedData = validateSocketEvent(CallResponseSchema, data);
      const call = this.activeCalls.get(validatedData.callId);

      if (!call || call.calleeId !== socket.userId) {
        socket.emit('call_error', createSocketError('Call not found or unauthorized'));
        return;
      }

      // Update call status
      await this.callingService.updateCallStatus(validatedData.callId, 'declined', {
        endedAt: new Date(),
      });

      // Create call event
      await this.callingService.createCallEvent({
        callId: validatedData.callId,
        eventType: 'call_declined',
        userId: socket.userId,
      });

      // Notify caller
      if (call.callerSocketId) {
        this.io.to(call.callerSocketId).emit('call_declined', {
          callId: validatedData.callId,
          timestamp: new Date().toISOString(),
        });
      }

      // Clean up call session
      console.log(`📞 Removing call ${validatedData.callId} from activeCalls (declined)`);
      this.activeCalls.delete(validatedData.callId);

      socket.emit('call_declined_success', { callId: validatedData.callId });

    } catch (error) {
      console.error('Error declining call:', error);
      socket.emit('call_error', createSocketError(
        error instanceof Error ? error.message : 'Failed to decline call'
      ));
    }
  }

  private async handleEndCall(socket: AuthenticatedSocket, data: unknown) {
    try {
      const validatedData = validateSocketEvent(CallResponseSchema, data);
      const call = this.activeCalls.get(validatedData.callId);

      if (!call) {
        socket.emit('call_error', createSocketError('Call not found'));
        return;
      }

      // Check if user is participant
      if (call.callerId !== socket.userId && call.calleeId !== socket.userId) {
        socket.emit('call_error', createSocketError('Not a participant in this call'));
        return;
      }

      // Update call status
      await this.callingService.updateCallStatus(validatedData.callId, 'ended', {
        endedAt: new Date(),
      });

      // Create call event
      await this.callingService.createCallEvent({
        callId: validatedData.callId,
        eventType: 'call_ended',
        userId: socket.userId,
      });

      // Notify other participant
      socket.to(`call:${validatedData.callId}`).emit('call_ended', {
        callId: validatedData.callId,
        endedBy: socket.userId,
        timestamp: new Date().toISOString(),
      });

      // Clean up call session
      console.log(`📞 Removing call ${validatedData.callId} from activeCalls (ended)`);
      this.activeCalls.delete(validatedData.callId);

      // Remove participants from call room
      this.io.in(`call:${validatedData.callId}`).socketsLeave(`call:${validatedData.callId}`);

      socket.emit('call_ended_success', { callId: validatedData.callId });

    } catch (error) {
      console.error('Error ending call:', error);
      socket.emit('call_error', createSocketError(
        error instanceof Error ? error.message : 'Failed to end call'
      ));
    }
  }

  private async handleWebRTCOffer(socket: AuthenticatedSocket, data: unknown) {
    try {
      console.log('🔥 WebRTC Offer received from user:', socket.userId, 'data:', data);
      const validatedData = validateSocketEvent(WebRTCOfferSchema, data);

      // Ensure sender is in the call room
      const callRoom = `call:${validatedData.callId}`;
      socket.join(callRoom);
      console.log(`📞 Ensured sender ${socket.userId} is in call room: ${callRoom}`);
      
      // Check current room members
      const roomMembers = this.io.sockets.adapter.rooms.get(callRoom);
      console.log(`📞 Call room ${callRoom} members:`, roomMembers ? Array.from(roomMembers) : 'none');

      // Forward offer to other participant
      console.log(`🔄 Forwarding WebRTC offer to call room: ${callRoom}`);
      socket.to(callRoom).emit('webrtc_offer', {
        callId: validatedData.callId,
        offer: validatedData.offer,
        from: socket.userId,
      });

      // Store SDP data without changing call status
      // Status should only change to 'active' after call is answered
      const currentCall = await this.callingService.getCall(validatedData.callId);
      if (currentCall) {
        await this.callingService.updateCallStatus(validatedData.callId, currentCall.status, {
          callerSdp: validatedData.offer.sdp,
        });
      }

    } catch (error) {
      console.error('Error handling WebRTC offer:', error);
      socket.emit('call_error', createSocketError(
        error instanceof Error ? error.message : 'Failed to handle WebRTC offer'
      ));
    }
  }

  private async handleWebRTCAnswer(socket: AuthenticatedSocket, data: unknown) {
    try {
      console.log('🔥 WebRTC Answer received from user:', socket.userId, 'data:', data);
      const validatedData = validateSocketEvent(WebRTCAnswerSchema, data);

      // Ensure sender is in the call room
      const callRoom = `call:${validatedData.callId}`;
      socket.join(callRoom);
      console.log(`📞 Ensured sender ${socket.userId} is in call room: ${callRoom}`);
      
      // Check current room members
      const roomMembers = this.io.sockets.adapter.rooms.get(callRoom);
      console.log(`📞 Call room ${callRoom} members:`, roomMembers ? Array.from(roomMembers) : 'none');

      // Forward answer to other participant
      console.log(`🔄 Forwarding WebRTC answer to call room: ${callRoom}`);
      socket.to(callRoom).emit('webrtc_answer', {
        callId: validatedData.callId,
        answer: validatedData.answer,
        from: socket.userId,
      });

      // Store SDP data and change status to 'active' if call was answered
      const currentCall = await this.callingService.getCall(validatedData.callId);
      if (currentCall) {
        // If call was answered and we now have both SDP data, set status to active
        const shouldActivate = currentCall.status === 'answered' && currentCall.callerSdp;
        await this.callingService.updateCallStatus(
          validatedData.callId, 
          shouldActivate ? 'active' : currentCall.status, 
          {
            calleeSdp: validatedData.answer.sdp,
          }
        );
        
        // Notify both participants when call becomes active
        if (shouldActivate) {
          console.log(`🔥 Emitting call_active event to room: ${callRoom}`);
          this.io.to(`call:${validatedData.callId}`).emit('call_active', {
            callId: validatedData.callId,
            timestamp: new Date().toISOString()
          });
          
          // Log room members for debugging
          const activeRoomMembers = this.io.sockets.adapter.rooms.get(callRoom);
          const members = activeRoomMembers ? Array.from(activeRoomMembers) : [];
          console.log(`📞 Call room ${callRoom} members after call_active: ${members.length}`, members);
        }
      }

    } catch (error) {
      console.error('Error handling WebRTC answer:', error);
      socket.emit('call_error', createSocketError(
        error instanceof Error ? error.message : 'Failed to handle WebRTC answer'
      ));
    }
  }

  private async handleWebRTCIceCandidate(socket: AuthenticatedSocket, data: unknown) {
    try {
      console.log('🔥 WebRTC ICE Candidate received from user:', socket.userId);
      const validatedData = validateSocketEvent(WebRTCIceCandidateSchema, data);

      // Ensure sender is in the call room
      const callRoom = `call:${validatedData.callId}`;
      socket.join(callRoom);
      console.log(`📞 Ensured sender ${socket.userId} is in call room: ${callRoom}`);
      
      // Check current room members
      const roomMembers = this.io.sockets.adapter.rooms.get(callRoom);
      console.log(`📞 Call room ${callRoom} members:`, roomMembers ? Array.from(roomMembers) : 'none');

      // Forward ICE candidate to other participant
      console.log(`🔄 Forwarding ICE candidate to call room: ${callRoom}`);
      socket.to(callRoom).emit('webrtc_ice_candidate', {
        callId: validatedData.callId,
        candidate: validatedData.candidate,
        from: socket.userId,
      });

      // Create call event for ICE candidate
      await this.callingService.createCallEvent({
        callId: validatedData.callId,
        eventType: 'ice_candidate',
        userId: socket.userId,
        eventData: { candidate: validatedData.candidate },
      });

    } catch (error) {
      console.error('Error handling ICE candidate:', error);
      socket.emit('call_error', createSocketError(
        error instanceof Error ? error.message : 'Failed to handle ICE candidate'
      ));
    }
  }

  private async handleMediaToggle(socket: AuthenticatedSocket, data: unknown) {
    try {
      const validatedData = validateSocketEvent(MediaToggleSchema, data);

      // Forward media toggle to other participant
      socket.to(`call:${validatedData.callId}`).emit('participant_media_toggle', {
        userId: socket.userId,
        mediaType: validatedData.mediaType,
        enabled: validatedData.enabled,
      });

      // Create call event for media toggle
      await this.callingService.createCallEvent({
        callId: validatedData.callId,
        eventType: 'media_toggle',
        userId: socket.userId,
        eventData: {
          mediaType: validatedData.mediaType,
          enabled: validatedData.enabled,
        },
      });

    } catch (error) {
      console.error('Error handling media toggle:', error);
      socket.emit('call_error', createSocketError(
        error instanceof Error ? error.message : 'Failed to handle media toggle'
      ));
    }
  }

  private async handleCallQualityReport(socket: AuthenticatedSocket, data: unknown) {
    try {
      const validatedData = validateSocketEvent(CallQualityReportSchema, data);

      // Store quality metrics
      await this.callingService.createQualityMetric({
        callId: validatedData.callId,
        userId: socket.userId,
        ...validatedData.metrics,
      });

      // Create call event for quality issue if metrics indicate problems
      const hasQualityIssues = (
        (validatedData.metrics.packetLoss && validatedData.metrics.packetLoss > 5) ||
        (validatedData.metrics.jitter && validatedData.metrics.jitter > 100) ||
        (validatedData.metrics.roundTripTime && validatedData.metrics.roundTripTime > 500)
      );

      if (hasQualityIssues) {
        await this.callingService.createCallEvent({
          callId: validatedData.callId,
          eventType: 'quality_issue',
          userId: socket.userId,
          eventData: validatedData.metrics,
        });
      }

    } catch (error) {
      console.error('Error handling call quality report:', error);
      socket.emit('call_error', createSocketError(
        error instanceof Error ? error.message : 'Failed to handle quality report'
      ));
    }
  }

  private async handleDisconnection(socket: AuthenticatedSocket) {
    try {
      // Find and clean up any active calls for this user
      for (const [callId, call] of this.activeCalls.entries()) {
        if (call.callerSocketId === socket.id || call.calleeSocketId === socket.id) {
          // Update call status to ended
          await this.callingService.updateCallStatus(callId, 'ended', {
            endedAt: new Date(),
          });

          // Create call event
          await this.callingService.createCallEvent({
            callId,
            eventType: 'call_ended',
            userId: socket.userId,
            eventData: { reason: 'participant_disconnected' },
          });

          // Notify other participant about disconnection
          socket.to(`call:${callId}`).emit('call_ended', {
            callId,
            reason: 'participant_disconnected',
            endedBy: socket.userId,
            timestamp: new Date().toISOString(),
          });

          // Clean up
          console.log(`📞 Removing call ${callId} from activeCalls (disconnect cleanup)`);
          this.activeCalls.delete(callId);
          this.io.in(`call:${callId}`).socketsLeave(`call:${callId}`);
        }
      }
    } catch (error) {
      console.error('Error handling disconnection cleanup:', error);
    }
  }

  // Helper method to notify about incoming calls (called from HTTP API)
  notifyIncomingCall(callData: {
    callId: string;
    caller: {
      id: string;
      username: string;
      firstName: string;
      lastName: string;
      profilePicture?: string;
    };
    calleeId: string;
    callType: 'audio' | 'video';
    conversationId: string;
    timestamp: string;
  }) {
    this.io.to(`user:${callData.calleeId}`).emit('incoming_call', {
      callId: callData.callId,
      caller: callData.caller,
      callType: callData.callType,
      conversationId: callData.conversationId,
      timestamp: callData.timestamp,
    });
  }
}
