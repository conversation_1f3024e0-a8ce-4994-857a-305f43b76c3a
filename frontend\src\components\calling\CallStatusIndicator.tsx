// frontend/src/components/calling/CallStatusIndicator.tsx
import React from 'react';
import { useSelector } from 'react-redux';
import { 
  Phone, 
  PhoneCall, 
  PhoneIncoming, 
  PhoneOff, 
  Loader2,
  Wifi,
  WifiOff
} from 'lucide-react';
import { selectCurrentCall, selectCallingState } from '../../store/slices/callingSlice';

export const CallStatusIndicator: React.FC = () => {
  const currentCall = useSelector(selectCurrentCall);
  const { isConnected } = useSelector(selectCallingState);

  const getStatusIcon = () => {
    if (!isConnected) {
      return <WifiOff className="w-4 h-4 text-red-500" />;
    }

    switch (currentCall.status) {
      case 'initiating':
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'calling':
        return <Phone className="w-4 h-4 text-blue-500 animate-pulse" />;
      case 'ringing':
        return <PhoneIncoming className="w-4 h-4 text-yellow-500 animate-pulse" />;
      case 'active':
        return <PhoneCall className="w-4 h-4 text-green-500" />;
      case 'ending':
        return <PhoneOff className="w-4 h-4 text-red-500" />;
      default:
        return <Phone className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusText = () => {
    if (!isConnected) {
      return 'Disconnected';
    }

    switch (currentCall.status) {
      case 'initiating':
        return 'Initiating...';
      case 'calling':
        return 'Calling...';
      case 'ringing':
        return 'Ringing...';
      case 'active':
        return 'Connected';
      case 'ending':
        return 'Ending...';
      default:
        return 'Idle';
    }
  };

  const getStatusColor = () => {
    if (!isConnected) {
      return 'text-red-500';
    }

    switch (currentCall.status) {
      case 'initiating':
        return 'text-blue-500';
      case 'calling':
        return 'text-blue-500';
      case 'ringing':
        return 'text-yellow-500';
      case 'active':
        return 'text-green-500';
      case 'ending':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <div className="flex items-center space-x-2">
      {getStatusIcon()}
      <span className={`text-sm font-medium ${getStatusColor()}`}>
        {getStatusText()}
      </span>
      {isConnected && (
        <div className="flex items-center space-x-1">
          <Wifi className="w-3 h-3 text-green-500" />
        </div>
      )}
    </div>
  );
};

export default CallStatusIndicator;
