import { test, expect, chromium, firefox, <PERSON><PERSON><PERSON>, BrowserContext, Page } from '@playwright/test';

// Test users - <PERSON> on Chrome, <PERSON> on Firefox as requested by user
const ALICE_USER = {
  username: '<EMAIL>',
  password: 'testpass123',
  email: '<EMAIL>'
};

const HARRY_USER = {
  username: '<EMAIL>',
  password: 'testpass123',
  email: '<EMAIL>'
};

async function loginUser(page: Page, user: { username: string; password: string; email: string }) {
  console.log(`🔐 Logging in ${user.username}...`);
  await page.goto('http://localhost:5000/login');
  await page.waitForSelector('[data-testid="email-input"]', { timeout: 10000 });
  await page.fill('[data-testid="email-input"]', user.email);
  await page.fill('[data-testid="password-input"]', user.password);
  await page.click('button[type="submit"]');

  // Wait for dashboard with more flexible URL matching
  await page.waitForFunction(() => window.location.pathname.includes('/dashboard'), { timeout: 15000 });
  console.log(`✅ ${user.username} logged in successfully`);
}

async function createOrFindConversation(page: Page, targetUsername: string): Promise<string> {
  console.log(`💬 Setting up conversation with ${targetUsername}...`);

  // Wait for dashboard to load
  await page.waitForSelector('[data-testid="conversation-list"]', { timeout: 10000 });

  // Try to find existing conversation first
  const existingConv = page.locator(`[data-testid="conversation-item"]:has-text("${targetUsername}")`);
  if (await existingConv.count() > 0) {
    console.log(`✅ Found existing conversation with ${targetUsername}`);
    await existingConv.first().click();
    await page.waitForURL('/dashboard/conversations/*');
    const url = page.url();
    const conversationId = url.split('/').pop();
    console.log(`✅ Using conversation: ${conversationId}`);
    return conversationId || '';
  }

  // Create new conversation
  await page.click('[data-testid="new-chat-button"]');
  await page.waitForSelector('[data-testid="user-search-modal"]', { timeout: 5000 });
  await page.fill('input[placeholder*="username"]', targetUsername);

  // Wait for search results and click on the user
  await page.waitForSelector('[data-testid="user-result"]', { timeout: 10000 });
  const userResult = page.locator('[data-testid="user-result"]').first();
  await userResult.locator('button').click();

  // Wait for conversation to be created and navigate
  await page.waitForTimeout(2000); // Give time for draft conversation creation
  
  const url = page.url();
  const conversationId = url.split('/').pop();
  return conversationId || '';
}

test.describe('Multi-Browser WebRTC Calling Tests', () => {
  let chromeBrowser: Browser;
  let firefoxBrowser: Browser;
  let aliceContext: BrowserContext;
  let harryContext: BrowserContext;
  let alicePage: Page;
  let harryPage: Page;

  test.beforeAll(async () => {
    console.log('🚀 Launching Chrome for Alice and Firefox for Harry...');
    
    // Launch Chrome for Alice
    chromeBrowser = await chromium.launch({ 
      headless: false,
      args: [
        '--use-fake-ui-for-media-stream',
        '--use-fake-device-for-media-stream',
        '--allow-running-insecure-content',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ]
    });

    // Launch Firefox for Harry
    firefoxBrowser = await firefox.launch({ 
      headless: false,
      firefoxUserPrefs: {
        'media.navigator.streams.fake': true,
        'media.navigator.permission.disabled': true
      }
    });

    // Create contexts - media permissions are handled via browser args/prefs
    aliceContext = await chromeBrowser.newContext();
    harryContext = await firefoxBrowser.newContext();

    // Create pages
    alicePage = await aliceContext.newPage();
    harryPage = await harryContext.newPage();

    // Enable console logging for debugging
    alicePage.on('console', msg => console.log(`ALICE (Chrome): ${msg.text()}`));
    harryPage.on('console', msg => console.log(`HARRY (Firefox): ${msg.text()}`));
    
    console.log('✅ Browsers launched successfully');
  });

  test.afterAll(async () => {
    console.log('🧹 Cleaning up browsers...');
    if (aliceContext) await aliceContext.close();
    if (harryContext) await harryContext.close();
    if (chromeBrowser) await chromeBrowser.close();
    if (firefoxBrowser) await firefoxBrowser.close();
  });

  test('Chrome-Firefox Audio Call Flow', async () => {
    // Step 1: Login both users
    console.log('🔐 Logging in users...');
    await Promise.all([
      loginUser(alicePage, ALICE_USER),
      loginUser(harryPage, HARRY_USER)
    ]);

    // Step 2: Alice creates/finds conversation with Harry
    console.log('💬 Setting up conversation...');
    const conversationId = await createOrFindConversation(alicePage, HARRY_USER.username);
    expect(conversationId).toBeTruthy();

    // Step 3: Harry navigates to the same conversation
    console.log(`🔗 Navigating Harry to conversation: ${conversationId}`);
    await harryPage.goto(`http://localhost:5000/dashboard/conversations/${conversationId}`);
    await harryPage.waitForLoadState('networkidle');

    // Step 4: Alice initiates audio call
    console.log('📞 Alice initiating audio call...');
    await alicePage.click('[data-testid="audio-call-btn"]');

    // Step 5: Verify Alice sees calling interface
    console.log('✅ Verifying Alice sees calling interface...');
    await expect(alicePage.locator('[data-testid="active-call-interface"]')).toBeVisible({ timeout: 2000 });

    // Step 6: Verify Harry receives incoming call
    console.log('📞 Verifying Harry receives incoming call...');
    await expect(harryPage.locator('[data-testid="incoming-call-modal"]')).toBeVisible({ timeout: 5000 });
    await expect(harryPage.locator('text=Audio Call')).toBeVisible();

    // Step 7: Harry answers the call
    console.log('✅ Harry answering call...');
    await harryPage.click('[data-testid="answer-call-btn"]');

    // Step 8: Verify Harry modal disappears and shows ActiveCallInterface
    console.log('🔄 Verifying Harry UI transition...');
    await expect(harryPage.locator('[data-testid="incoming-call-modal"]')).not.toBeVisible({ timeout: 2000 });
    await expect(harryPage.locator('[data-testid="active-call-interface"]')).toBeVisible({ timeout: 2000 });

    // Step 9: Verify both parties show "Connected" status
    console.log('🔗 Verifying connection status...');
    await expect(alicePage.locator('text=Connected')).toBeVisible({ timeout: 15000 });
    await expect(harryPage.locator('text=Connected')).toBeVisible({ timeout: 15000 });

    // Step 10: Verify WebRTC connection is established
    console.log('🌐 Verifying WebRTC connection...');
    
    // Check for WebRTC peer connection in both pages
    const alicePeerConnection = await alicePage.evaluate(() => {
      return window.webrtcService?.peerConnection?.connectionState;
    });
    
    const harryPeerConnection = await harryPage.evaluate(() => {
      return window.webrtcService?.peerConnection?.connectionState;
    });

    console.log(`Alice peer connection state: ${alicePeerConnection}`);
    console.log(`Harry peer connection state: ${harryPeerConnection}`);

    // Both should be 'connected' or 'completed'
    expect(['connected', 'completed']).toContain(alicePeerConnection);
    expect(['connected', 'completed']).toContain(harryPeerConnection);

    // Step 11: Test call termination
    console.log('📞 Testing call termination...');
    await alicePage.click('[data-testid="end-call-btn"]');

    // Verify both interfaces disappear
    await expect(alicePage.locator('[data-testid="active-call-interface"]')).not.toBeVisible({ timeout: 2000 });
    await expect(harryPage.locator('[data-testid="active-call-interface"]')).not.toBeVisible({ timeout: 2000 });

    console.log('✅ Multi-browser audio call test completed successfully!');
  });
});
