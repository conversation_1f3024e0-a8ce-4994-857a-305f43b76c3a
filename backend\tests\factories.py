# backend/tests/factories.py
import factory
import uuid
import base64
import secrets
from django.contrib.auth import get_user_model
from messaging.models import Conversation, ConversationParticipant, Message
from encryption.models import UserKeyBundle, OneTimePreKey, ConversationSession, MessageKey, KeyBundleUploadLog, GroupSession
from media.models import MediaFile, MediaDownload, MediaChunk, MediaProcessingJob
from calling.models import Call, CallParticipant, CallEvent, CallQualityMetric
from faker import Faker

User = get_user_model()
fake = Faker()

class UserFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = User
        skip_postgeneration_save = True

    id = factory.LazyFunction(uuid.uuid4)
    email = factory.Sequence(lambda n: f"user{n}@example.com")
    username = factory.Sequence(lambda n: f"user{n}")
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')
    password = factory.PostGenerationMethodCall('set_password', 'testpass123')
    is_verified = True
    is_active = True

class ConversationFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Conversation

    id = factory.LazyFunction(uuid.uuid4)
    type = 'DIRECT'
    name = None

class GroupConversationFactory(ConversationFactory):
    type = 'GROUP'
    name = factory.Faker('sentence', nb_words=3)

class ConversationParticipantFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ConversationParticipant

    id = factory.LazyFunction(uuid.uuid4)
    conversation = factory.SubFactory(ConversationFactory)
    user = factory.SubFactory(UserFactory)
    role = 'MEMBER'

class MessageFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Message

    id = factory.LazyFunction(uuid.uuid4)
    conversation = factory.SubFactory(ConversationFactory)
    sender = factory.SubFactory(UserFactory)
    content = factory.Faker('text', max_nb_chars=200)
    message_type = 'TEXT'

# Encryption Factories
class UserKeyBundleFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = UserKeyBundle

    id = factory.LazyFunction(uuid.uuid4)
    user = factory.SubFactory(UserFactory)
    identity_public_key = factory.LazyFunction(lambda: base64.b64encode(secrets.token_bytes(91)).decode())
    signed_prekey_id = factory.Sequence(lambda n: n)
    signed_prekey_public = factory.LazyFunction(lambda: base64.b64encode(secrets.token_bytes(91)).decode())
    signed_prekey_signature = factory.LazyFunction(lambda: base64.b64encode(secrets.token_bytes(64)).decode())

class OneTimePreKeyFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = OneTimePreKey

    id = factory.LazyFunction(uuid.uuid4)
    user = factory.SubFactory(UserFactory)
    key_id = factory.Sequence(lambda n: n)
    public_key = factory.LazyFunction(lambda: base64.b64encode(secrets.token_bytes(91)).decode())
    is_used = False

class ConversationSessionFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ConversationSession

    id = factory.LazyFunction(uuid.uuid4)
    conversation = factory.SubFactory(ConversationFactory)
    participant = factory.SubFactory(UserFactory)
    session_state = factory.LazyFunction(lambda: {"test": "data"})
    root_key = factory.LazyFunction(lambda: base64.b64encode(secrets.token_bytes(32)).decode())

class MessageKeyFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = MessageKey

    id = factory.LazyFunction(uuid.uuid4)
    session = factory.SubFactory(ConversationSessionFactory)
    message_number = factory.Sequence(lambda n: n)
    message_key = factory.LazyFunction(lambda: base64.b64encode(secrets.token_bytes(32)).decode())

class KeyBundleUploadLogFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = KeyBundleUploadLog

    id = factory.LazyFunction(uuid.uuid4)
    user = factory.SubFactory(UserFactory)
    ip_address = factory.Faker('ipv4')
    success = True
    error_message = ""

class GroupSessionFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = GroupSession

    id = factory.LazyFunction(uuid.uuid4)
    conversation = factory.SubFactory(GroupConversationFactory)
    session_id = factory.LazyFunction(lambda: f"group_{uuid.uuid4()}")
    current_epoch = 0
    encrypted_group_key = factory.LazyFunction(lambda: base64.b64encode(secrets.token_bytes(32)).decode())
    group_key_iv = factory.LazyFunction(lambda: base64.b64encode(secrets.token_bytes(12)).decode())

# Media Factories
class MediaFileFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = MediaFile

    id = factory.LazyFunction(uuid.uuid4)
    message = factory.SubFactory(MessageFactory)
    uploader = factory.SubFactory(UserFactory)
    original_filename = factory.Faker('file_name')
    file_type = 'image'
    mime_type = 'image/jpeg'
    file_size = factory.Faker('random_int', min=1000, max=1000000)
    encrypted_file_path = factory.LazyFunction(lambda: f"media/test/{uuid.uuid4()}.enc")
    wrapped_file_key = factory.LazyFunction(lambda: base64.b64encode(secrets.token_bytes(32)).decode())
    file_nonce = factory.LazyFunction(lambda: base64.b64encode(secrets.token_bytes(12)).decode())
    processing_status = 'completed'
    virus_scan_status = 'completed'

class MediaDownloadFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = MediaDownload

    id = factory.LazyFunction(uuid.uuid4)
    media_file = factory.SubFactory(MediaFileFactory)
    downloaded_by = factory.SubFactory(UserFactory)
    download_token = factory.LazyFunction(lambda: secrets.token_urlsafe(32))
    expires_at = factory.Faker('future_datetime', end_date='+1d')
    ip_address = factory.Faker('ipv4')

class MediaChunkFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = MediaChunk

    id = factory.LazyFunction(uuid.uuid4)
    upload_session = factory.LazyFunction(lambda: str(uuid.uuid4()))
    chunk_number = 0
    total_chunks = 1
    chunk_data = factory.LazyFunction(lambda: b'test chunk data')
    chunk_hash = factory.LazyFunction(lambda: secrets.token_hex(32))

class MediaProcessingJobFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = MediaProcessingJob

    id = factory.LazyFunction(uuid.uuid4)
    media_file = factory.SubFactory(MediaFileFactory)
    job_type = 'thumbnail'
    status = 'pending'
    priority = 5

# Calling Factories
class CallFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Call

    id = factory.LazyFunction(uuid.uuid4)
    conversation = factory.SubFactory(ConversationFactory)
    caller = factory.SubFactory(UserFactory)
    callee = factory.SubFactory(UserFactory)
    call_type = 'audio'
    status = 'initiated'
    session_id = factory.LazyFunction(lambda: f"call_{uuid.uuid4()}")

class CallParticipantFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = CallParticipant

    id = factory.LazyFunction(uuid.uuid4)
    call = factory.SubFactory(CallFactory)
    user = factory.SubFactory(UserFactory)
    status = 'invited'

class CallEventFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = CallEvent

    id = factory.LazyFunction(uuid.uuid4)
    call = factory.SubFactory(CallFactory)
    event_type = 'call_initiated'
    user = factory.SubFactory(UserFactory)
    event_data = factory.LazyFunction(lambda: {})

class CallQualityMetricFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = CallQualityMetric

    id = factory.LazyFunction(uuid.uuid4)
    call = factory.SubFactory(CallFactory)
    user = factory.SubFactory(UserFactory)
    packet_loss = factory.Faker('pyfloat', min_value=0, max_value=10)
    jitter = factory.Faker('pyfloat', min_value=0, max_value=100)
    round_trip_time = factory.Faker('pyfloat', min_value=10, max_value=500)
