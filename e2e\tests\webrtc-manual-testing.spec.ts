// e2e/tests/webrtc-manual-testing.spec.ts
import { test, expect, chromium, Browser, BrowserContext, Page } from '@playwright/test';

// Test users - both using Chrome as requested
const ALICE_USER = {
  username: '<EMAIL>',
  password: 'testpass123',
  email: '<EMAIL>'
};

const HARRY_USER = {
  username: '<EMAIL>',
  password: 'testpass123',
  email: '<EMAIL>'
};

// Test configuration
const FRONTEND_URL = 'http://localhost:5000';
const TEST_TIMEOUT = 300000; // 5 minutes for manual testing

test.describe('WebRTC Manual Testing - Two Chrome Windows', () => {
  let aliceBrowser: Browser;
  let harryBrowser: Browser;
  let aliceContext: BrowserContext;
  let harryContext: BrowserContext;
  let alicePage: Page;
  let harryPage: Page;

  test.beforeAll(async () => {
    console.log('🚀 Setting up WebRTC Manual Testing Environment...');
    
    // Launch Chrome for <PERSON> with WebRTC permissions (REAL DEVICES)
    console.log('🌟 Launching Chrome browser for Alice with real audio devices...');
    aliceBrowser = await chromium.launch({ 
      headless: false,
      args: [
        '--allow-running-insecure-content',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--autoplay-policy=no-user-gesture-required',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--enable-logging=stderr',
        '--vmodule=*/webrtc/*=1'
      ]
    });

    // Launch Chrome for Harry with WebRTC permissions (REAL DEVICES)
    console.log('🌟 Launching Chrome browser for Harry with real audio devices...');
    harryBrowser = await chromium.launch({ 
      headless: false,
      args: [
        '--allow-running-insecure-content',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--autoplay-policy=no-user-gesture-required',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--enable-logging=stderr',
        '--vmodule=*/webrtc/*=1'
      ]
    });

    // Create contexts with media permissions
    aliceContext = await aliceBrowser.newContext({
      permissions: ['camera', 'microphone'],
      viewport: { width: 1280, height: 720 }
    });

    harryContext = await harryBrowser.newContext({
      permissions: ['camera', 'microphone'],
      viewport: { width: 1280, height: 720 }
    });

    // Create pages
    alicePage = await aliceContext.newPage();
    harryPage = await harryContext.newPage();

    // Enable console logging for debugging
    alicePage.on('console', msg => {
      console.log(`🟦 ALICE CONSOLE [${msg.type()}]:`, msg.text());
    });

    harryPage.on('console', msg => {
      console.log(`🟨 HARRY CONSOLE [${msg.type()}]:`, msg.text());
    });

    // Enable error logging
    alicePage.on('pageerror', error => {
      console.error('🔴 ALICE PAGE ERROR:', error.message);
    });

    harryPage.on('pageerror', error => {
      console.error('🔴 HARRY PAGE ERROR:', error.message);
    });

    // Enable request/response logging for API debugging
    alicePage.on('request', request => {
      if (request.url().includes('/calling/')) {
        console.log(`🟦 ALICE API REQUEST: ${request.method()} ${request.url()}`);
      }
    });

    alicePage.on('response', response => {
      if (response.url().includes('/calling/')) {
        console.log(`🟦 ALICE API RESPONSE: ${response.status()} ${response.url()}`);
      }
    });

    harryPage.on('request', request => {
      if (request.url().includes('/calling/')) {
        console.log(`🟨 HARRY API REQUEST: ${request.method()} ${request.url()}`);
      }
    });

    harryPage.on('response', response => {
      if (response.url().includes('/calling/')) {
        console.log(`🟨 HARRY API RESPONSE: ${response.status()} ${response.url()}`);
      }
    });

    console.log('✅ Browser setup complete');
  });

  test.afterAll(async () => {
    console.log('🧹 Cleaning up browsers...');
    await aliceContext?.close();
    await harryContext?.close();
    await aliceBrowser?.close();
    await harryBrowser?.close();
    console.log('✅ Cleanup complete');
  });

  test('Manual WebRTC Calling Test', async () => {
    test.setTimeout(TEST_TIMEOUT);

    console.log('🔐 Logging in users...');

    // Login Alice
    console.log('🟦 Logging in Alice...');
    await alicePage.goto(`${FRONTEND_URL}/login`);
    await alicePage.fill('[data-testid="email-input"], input[type="email"]', ALICE_USER.email);
    await alicePage.fill('[data-testid="password-input"], input[type="password"]', ALICE_USER.password);
    await alicePage.click('[data-testid="login-button"], button[type="submit"]');
    await alicePage.waitForURL('**/dashboard', { timeout: 10000 });
    console.log('✅ Alice logged in successfully');

    // Login Harry
    console.log('🟨 Logging in Harry...');
    await harryPage.goto(`${FRONTEND_URL}/login`);
    await harryPage.fill('[data-testid="email-input"], input[type="email"]', HARRY_USER.email);
    await harryPage.fill('[data-testid="password-input"], input[type="password"]', HARRY_USER.password);
    await harryPage.click('[data-testid="login-button"], button[type="submit"]');
    await harryPage.waitForURL('**/dashboard', { timeout: 10000 });
    console.log('✅ Harry logged in successfully');

    // Add debug logging to frontend applications
    await alicePage.evaluate(() => {
      // Enable verbose WebRTC logging
      (window as any).webrtcDebug = true;
      console.log('🔧 Alice: WebRTC debug logging enabled');
      
      // Log all socket events
      if ((window as any).socket) {
        const originalEmit = (window as any).socket.emit;
        (window as any).socket.emit = function(...args: any[]) {
          console.log('📤 Alice Socket EMIT:', args[0], args.slice(1));
          return originalEmit.apply(this, args);
        };

        const originalOn = (window as any).socket.on;
        (window as any).socket.on = function(event: string, handler: Function) {
          const wrappedHandler = function(...args: any[]) {
            console.log('📥 Alice Socket RECEIVE:', event, args);
            return handler.apply(this, args);
          };
          return originalOn.call(this, event, wrappedHandler);
        };
      }
    });

    await harryPage.evaluate(() => {
      // Enable verbose WebRTC logging
      (window as any).webrtcDebug = true;
      console.log('🔧 Harry: WebRTC debug logging enabled');
      
      // Log all socket events
      if ((window as any).socket) {
        const originalEmit = (window as any).socket.emit;
        (window as any).socket.emit = function(...args: any[]) {
          console.log('📤 Harry Socket EMIT:', args[0], args.slice(1));
          return originalEmit.apply(this, args);
        };

        const originalOn = (window as any).socket.on;
        (window as any).socket.on = function(event: string, handler: Function) {
          const wrappedHandler = function(...args: any[]) {
            console.log('📥 Harry Socket RECEIVE:', event, args);
            return handler.apply(this, args);
          };
          return originalOn.call(this, event, wrappedHandler);
        };
      }
    });

    console.log('🎯 MANUAL TESTING READY!');
    console.log('');
    console.log('📋 TESTING INSTRUCTIONS:');
    console.log('1. Two Chrome windows are now open with Alice and Harry logged in');
    console.log('2. Navigate to a conversation between Alice and Harry');
    console.log('3. Click the call button to initiate a call');
    console.log('4. Accept the call on the other side');
    console.log('5. Test audio transmission and call controls');
    console.log('6. End the call and verify media cleanup');
    console.log('');
    console.log('🔍 DEBUGGING INFO:');
    console.log('- All console logs are captured and displayed here');
    console.log('- API requests/responses are logged');
    console.log('- Socket events are logged');
    console.log('- WebRTC debug logging is enabled');
    console.log('');
    console.log('⏰ Test will wait for 5 minutes for manual interaction...');

    // Wait for manual testing
    await new Promise(resolve => setTimeout(resolve, TEST_TIMEOUT - 10000));

    console.log('⏰ Manual testing time is almost up (10 seconds remaining)...');
    await new Promise(resolve => setTimeout(resolve, 10000));

    console.log('✅ Manual testing session completed');
  });
});
