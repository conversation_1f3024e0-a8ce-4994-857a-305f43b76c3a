// frontend/src/services/callingApi.ts
import { api } from './api';
import type { ApiResponse, PaginatedResponse } from '../types';

// Call-related types
export interface CallParticipant {
  id: string;
  username: string;
  first_name: string;
  last_name: string;
  profile_picture?: string;
}

export interface Call {
  id: string;
  conversation_id: string;
  caller_id: string;
  call_type: 'audio' | 'video';
  status: 'initiated' | 'ringing' | 'active' | 'ended' | 'declined' | 'failed';
  started_at: string;
  ended_at?: string;
  duration?: number;
  participants: CallParticipant[];
  caller: CallParticipant;
  created_at: string;
  updated_at: string;
}

export interface CallHistory {
  id: string;
  conversation_id: string;
  call_type: 'audio' | 'video';
  status: 'completed' | 'missed' | 'declined' | 'failed';
  duration: number;
  started_at: string;
  ended_at: string;
  participants: CallParticipant[];
  caller: CallParticipant;
}

export interface CallQualityReport {
  call_id: string;
  audio_quality: number; // 1-5 rating
  video_quality?: number; // 1-5 rating
  connection_quality: number; // 1-5 rating
  issues?: string[];
  feedback?: string;
}

export interface InitiateCallRequest {
  conversation_id: string;
  call_type: 'audio' | 'video';
}

export interface InitiateCallResponse {
  call_id: string;
  status: string;
  message: string;
}

export interface UpdateCallStatusRequest {
  status: 'answered' | 'declined' | 'ended' | 'failed';
  ended_at?: string;
  duration?: number;
}

// Calling API endpoints
export const callingApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get call detail
    getCallDetail: builder.query<ApiResponse<Call>, string>({
      query: (callId) => `/calling/${callId}/`,
      providesTags: (result, error, callId) => [
        { type: 'Call', id: callId },
      ],
      transformResponse: (response: any) => {
        // Handle the API response format with success/data wrapper
        if (response.success && response.data) {
          return response;
        }
        // Handle direct response
        return { success: true, data: response };
      },
    }),

    // Get call history for a conversation
    getCallHistory: builder.query<PaginatedResponse<CallHistory>, { conversationId: string; page?: number; limit?: number }>({
      query: ({ conversationId, page = 1, limit = 20 }) => 
        `/calling/history/?conversation_id=${conversationId}&page=${page}&limit=${limit}`,
      providesTags: (result, error, { conversationId }) => [
        { type: 'CallHistory', id: conversationId },
        ...(result?.data?.results || []).map((call) => ({
          type: 'CallHistory' as const,
          id: call.id,
        })),
      ],
      transformResponse: (response: any) => {
        // Handle the API response format with success/data wrapper
        if (response.success && response.data) {
          return response.data;
        }
        // Handle legacy paginated responses
        return response;
      },
    }),

    // Get all call history for current user
    getAllCallHistory: builder.query<PaginatedResponse<CallHistory>, { page?: number; limit?: number }>({
      query: ({ page = 1, limit = 20 }) => 
        `/calling/history/?page=${page}&limit=${limit}`,
      providesTags: (result) => [
        { type: 'CallHistory', id: 'LIST' },
        ...(result?.data?.results || []).map((call) => ({
          type: 'CallHistory' as const,
          id: call.id,
        })),
      ],
      transformResponse: (response: any) => {
        // Handle the API response format with success/data wrapper
        if (response.success && response.data) {
          return response.data;
        }
        // Handle legacy paginated responses
        return response;
      },
    }),

    // Initiate a call
    initiateCall: builder.mutation<ApiResponse<InitiateCallResponse>, InitiateCallRequest>({
      query: (data) => ({
        url: '/calling/initiate/',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { conversation_id }) => [
        { type: 'CallHistory', id: conversation_id },
        { type: 'CallHistory', id: 'LIST' },
      ],
      transformResponse: (response: any) => {
        // Handle the API response format with success/data wrapper
        if (response.success && response.data) {
          return response;
        }
        // Handle direct response
        return { success: true, data: response };
      },
    }),

    // Update call status
    updateCallStatus: builder.mutation<ApiResponse<Call>, { callId: string } & UpdateCallStatusRequest>({
      query: ({ callId, ...data }) => ({
        url: `/calling/${callId}/`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (result, error, { callId }) => [
        { type: 'Call', id: callId },
        { type: 'CallHistory', id: 'LIST' },
      ],
      transformResponse: (response: any) => {
        // Handle the API response format with success/data wrapper
        if (response.success && response.data) {
          return response;
        }
        // Handle direct response
        return { success: true, data: response };
      },
    }),

    // Report call quality
    reportCallQuality: builder.mutation<ApiResponse<{ message: string }>, CallQualityReport>({
      query: (data) => ({
        url: `/calling/${data.call_id}/quality/`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { call_id }) => [
        { type: 'Call', id: call_id },
      ],
      transformResponse: (response: any) => {
        // Handle the API response format with success/data wrapper
        if (response.success && response.data) {
          return response;
        }
        // Handle direct response
        return { success: true, data: response };
      },
    }),

    // Get call statistics
    getCallStatistics: builder.query<ApiResponse<{
      total_calls: number;
      total_duration: number;
      audio_calls: number;
      video_calls: number;
      missed_calls: number;
      average_duration: number;
    }>, { period?: 'day' | 'week' | 'month' | 'year' }>({
      query: ({ period = 'month' }) => `/calling/statistics/?period=${period}`,
      providesTags: [{ type: 'CallHistory', id: 'STATS' }],
      transformResponse: (response: any) => {
        // Handle the API response format with success/data wrapper
        if (response.success && response.data) {
          return response;
        }
        // Handle direct response
        return { success: true, data: response };
      },
    }),

    // End call
    endCall: builder.mutation<ApiResponse<Call>, { callId: string; duration?: number }>({
      query: ({ callId, duration }) => ({
        url: `/calling/${callId}/end/`,
        method: 'POST',
        body: { duration },
      }),
      invalidatesTags: (result, error, { callId }) => [
        { type: 'Call', id: callId },
        { type: 'CallHistory', id: 'LIST' },
      ],
      transformResponse: (response: any) => {
        // Handle the API response format with success/data wrapper
        if (response.success && response.data) {
          return response;
        }
        // Handle direct response
        return { success: true, data: response };
      },
    }),

    // Answer call
    answerCall: builder.mutation<ApiResponse<Call>, { callId: string }>({
      query: ({ callId }) => ({
        url: `/calling/${callId}/answer/`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, { callId }) => [
        { type: 'Call', id: callId },
      ],
      transformResponse: (response: any) => {
        // Handle the API response format with success/data wrapper
        if (response.success && response.data) {
          return response;
        }
        // Handle direct response
        return { success: true, data: response };
      },
    }),

    // Decline call
    declineCall: builder.mutation<ApiResponse<Call>, { callId: string }>({
      query: ({ callId }) => ({
        url: `/calling/${callId}/decline/`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, { callId }) => [
        { type: 'Call', id: callId },
        { type: 'CallHistory', id: 'LIST' },
      ],
      transformResponse: (response: any) => {
        // Handle the API response format with success/data wrapper
        if (response.success && response.data) {
          return response;
        }
        // Handle direct response
        return { success: true, data: response };
      },
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useGetCallDetailQuery,
  useGetCallHistoryQuery,
  useGetAllCallHistoryQuery,
  useInitiateCallMutation,
  useUpdateCallStatusMutation,
  useReportCallQualityMutation,
  useGetCallStatisticsQuery,
  useEndCallMutation,
  useAnswerCallMutation,
  useDeclineCallMutation,
} = callingApi;
