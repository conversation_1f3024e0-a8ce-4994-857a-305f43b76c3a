# backend/tests/test_encryption_views.py
import pytest
import json
import base64
import secrets
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from unittest.mock import patch, MagicMock

from encryption.models import UserKeyBundle, OneTimePreKey, KeyBundleUploadLog
from tests.factories import UserFactory, UserKeyBundleFactory, OneTimePreKeyFactory

User = get_user_model()


@pytest.mark.django_db
class TestEncryptionViews:
    """Test encryption endpoints"""

    def setup_method(self):
        """Set up test data"""
        self.client = APIClient()
        self.user = UserFactory()
        self.other_user = UserFactory()
        
        # Authenticate client
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

    def generate_test_key_bundle_data(self):
        """Generate test key bundle data"""
        return {
            'identity_public_key': base64.b64encode(secrets.token_bytes(91)).decode(),
            'signed_prekey_id': 1,
            'signed_prekey_public': base64.b64encode(secrets.token_bytes(91)).decode(),
            'signed_prekey_signature': base64.b64encode(secrets.token_bytes(64)).decode()
        }

    @patch('encryption.views.verify_signed_prekey_signature')
    @patch('encryption.views.validate_ecdh_public_key')
    def test_upload_key_bundle_success(self, mock_validate_key, mock_verify_sig):
        """Test successful key bundle upload"""
        mock_verify_sig.return_value = True
        mock_validate_key.return_value = True
        
        data = self.generate_test_key_bundle_data()
        
        url = reverse('encryption:upload_key_bundle')
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        assert 'key_bundle_id' in response.data
        assert 'message' in response.data
        
        # Verify key bundle was created in database
        key_bundle = UserKeyBundle.objects.get(user=self.user)
        assert key_bundle.identity_public_key == data['identity_public_key']
        assert key_bundle.signed_prekey_id == data['signed_prekey_id']

    @patch('encryption.views.verify_signed_prekey_signature')
    @patch('encryption.views.validate_ecdh_public_key')
    def test_upload_key_bundle_invalid_signature(self, mock_validate_key, mock_verify_sig):
        """Test key bundle upload with invalid signature"""
        mock_verify_sig.return_value = False
        mock_validate_key.return_value = True
        
        data = self.generate_test_key_bundle_data()
        
        url = reverse('encryption:upload_key_bundle')
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data['error'] == 'Invalid signed pre-key signature'
        assert response.data['code'] == 'SIGNATURE_INVALID'

    @patch('encryption.views.verify_signed_prekey_signature')
    @patch('encryption.views.validate_ecdh_public_key')
    def test_upload_key_bundle_invalid_key_format(self, mock_validate_key, mock_verify_sig):
        """Test key bundle upload with invalid key format"""
        mock_verify_sig.return_value = True
        mock_validate_key.return_value = False
        
        data = self.generate_test_key_bundle_data()
        
        url = reverse('encryption:upload_key_bundle')
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data['error'] == 'Invalid signed pre-key format'
        assert response.data['code'] == 'KEY_FORMAT_INVALID'

    def test_upload_key_bundle_unauthenticated(self):
        """Test key bundle upload without authentication"""
        self.client.credentials()  # Remove authentication
        
        data = self.generate_test_key_bundle_data()
        
        url = reverse('encryption:upload_key_bundle')
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_upload_key_bundle_invalid_data(self):
        """Test key bundle upload with invalid data"""
        data = {
            'identity_public_key': 'invalid_base64',
            'signed_prekey_id': 'not_an_integer',
            'signed_prekey_public': '',
            'signed_prekey_signature': ''
        }
        
        url = reverse('encryption:upload_key_bundle')
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'error' in response.data

    @patch('encryption.views.verify_signed_prekey_signature')
    @patch('encryption.views.validate_ecdh_public_key')
    def test_upload_key_bundle_replaces_existing(self, mock_validate_key, mock_verify_sig):
        """Test that uploading a new key bundle replaces the existing one"""
        mock_verify_sig.return_value = True
        mock_validate_key.return_value = True
        
        # Create existing key bundle
        UserKeyBundleFactory(user=self.user)
        
        data = self.generate_test_key_bundle_data()
        
        url = reverse('encryption:upload_key_bundle')
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        
        # Verify only one key bundle exists for user
        key_bundles = UserKeyBundle.objects.filter(user=self.user)
        assert key_bundles.count() == 1
        assert key_bundles.first().identity_public_key == data['identity_public_key']

    def test_get_key_bundle_success(self):
        """Test successful key bundle retrieval"""
        key_bundle = UserKeyBundleFactory(user=self.other_user)
        
        url = reverse('encryption:get_key_bundle', kwargs={'user_id': self.other_user.id})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['identity_public_key'] == key_bundle.identity_public_key
        assert 'signed_prekey' in response.data
        assert response.data['signed_prekey']['id'] == key_bundle.signed_prekey_id

    def test_get_key_bundle_with_one_time_prekey(self):
        """Test key bundle retrieval with one-time pre-key consumption"""
        key_bundle = UserKeyBundleFactory(user=self.other_user)
        prekey = OneTimePreKeyFactory(user=self.other_user, is_used=False)
        
        url = reverse('encryption:get_key_bundle', kwargs={'user_id': self.other_user.id})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'one_time_prekey' in response.data
        assert response.data['one_time_prekey']['id'] == prekey.key_id
        
        # Verify pre-key was marked as used
        prekey.refresh_from_db()
        assert prekey.is_used is True
        assert prekey.used_at is not None

    def test_get_key_bundle_not_found(self):
        """Test key bundle retrieval for user without key bundle"""
        url = reverse('encryption:get_key_bundle', kwargs={'user_id': self.other_user.id})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.data['error'] == 'Key bundle not found for user'
        assert response.data['code'] == 'KEY_BUNDLE_NOT_FOUND'

    def test_get_key_bundle_user_not_found(self):
        """Test key bundle retrieval for non-existent user"""
        url = reverse('encryption:get_key_bundle', kwargs={'user_id': '00000000-0000-0000-0000-000000000000'})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_get_key_bundle_unauthenticated(self):
        """Test key bundle retrieval without authentication"""
        self.client.credentials()  # Remove authentication
        
        url = reverse('encryption:get_key_bundle', kwargs={'user_id': self.other_user.id})
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @patch('encryption.views.validate_ecdh_public_key')
    def test_upload_one_time_prekeys_success(self, mock_validate_key):
        """Test successful one-time pre-keys upload"""
        mock_validate_key.return_value = True
        
        data = {
            'prekeys': [
                {
                    'key_id': 1,
                    'public_key': base64.b64encode(secrets.token_bytes(91)).decode()
                },
                {
                    'key_id': 2,
                    'public_key': base64.b64encode(secrets.token_bytes(91)).decode()
                }
            ]
        }
        
        url = reverse('encryption:upload_one_time_prekeys')
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['count'] == 2
        assert '2 one-time pre-keys uploaded successfully' in response.data['message']
        
        # Verify pre-keys were created in database
        prekeys = OneTimePreKey.objects.filter(user=self.user)
        assert prekeys.count() == 2

    @patch('encryption.views.validate_ecdh_public_key')
    def test_upload_one_time_prekeys_invalid_key_format(self, mock_validate_key):
        """Test one-time pre-keys upload with invalid key format"""
        mock_validate_key.return_value = False
        
        data = {
            'prekeys': [
                {
                    'key_id': 1,
                    'public_key': 'invalid_key'
                }
            ]
        }
        
        url = reverse('encryption:upload_one_time_prekeys')
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'Invalid ECDH public key format' in response.data['error']
        assert response.data['code'] == 'KEY_FORMAT_INVALID'

    @patch('encryption.views.validate_ecdh_public_key')
    def test_upload_one_time_prekeys_duplicate_key_id(self, mock_validate_key):
        """Test one-time pre-keys upload with duplicate key ID"""
        mock_validate_key.return_value = True
        
        # Create existing pre-key
        OneTimePreKeyFactory(user=self.user, key_id=1)
        
        data = {
            'prekeys': [
                {
                    'key_id': 1,  # Duplicate
                    'public_key': base64.b64encode(secrets.token_bytes(91)).decode()
                }
            ]
        }
        
        url = reverse('encryption:upload_one_time_prekeys')
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'Key ID 1 already exists' in response.data['error']
        assert response.data['code'] == 'DUPLICATE_KEY_ID'

    def test_upload_one_time_prekeys_unauthenticated(self):
        """Test one-time pre-keys upload without authentication"""
        self.client.credentials()  # Remove authentication
        
        data = {
            'prekeys': [
                {
                    'key_id': 1,
                    'public_key': base64.b64encode(secrets.token_bytes(91)).decode()
                }
            ]
        }
        
        url = reverse('encryption:upload_one_time_prekeys')
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_upload_one_time_prekeys_invalid_data(self):
        """Test one-time pre-keys upload with invalid data"""
        data = {
            'prekeys': [
                {
                    'key_id': 'not_an_integer',
                    'public_key': ''
                }
            ]
        }
        
        url = reverse('encryption:upload_one_time_prekeys')
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'error' in response.data

    def test_get_prekey_count_success(self):
        """Test successful pre-key count retrieval"""
        # Create some pre-keys
        OneTimePreKeyFactory(user=self.user, is_used=False)
        OneTimePreKeyFactory(user=self.user, is_used=False)
        OneTimePreKeyFactory(user=self.user, is_used=True)
        
        url = reverse('encryption:get_prekey_count')
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['available_count'] == 2
        assert response.data['total_count'] == 3
        assert response.data['used_count'] == 1

    def test_get_prekey_count_empty(self):
        """Test pre-key count retrieval when user has no pre-keys"""
        url = reverse('encryption:get_prekey_count')
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['available_count'] == 0
        assert response.data['total_count'] == 0
        assert response.data['used_count'] == 0

    def test_get_prekey_count_unauthenticated(self):
        """Test pre-key count retrieval without authentication"""
        self.client.credentials()  # Remove authentication
        
        url = reverse('encryption:get_prekey_count')
        response = self.client.get(url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @patch('encryption.views.get_client_ip')
    @patch('encryption.views.is_rate_limited')
    def test_rate_limiting(self, mock_rate_limited, mock_get_ip):
        """Test rate limiting functionality"""
        mock_get_ip.return_value = '127.0.0.1'
        mock_rate_limited.return_value = True
        
        data = self.generate_test_key_bundle_data()
        
        url = reverse('encryption:upload_key_bundle')
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_429_TOO_MANY_REQUESTS
        assert 'Rate limit exceeded' in response.data['error']

    @patch('encryption.views.log_key_bundle_operation')
    @patch('encryption.views.verify_signed_prekey_signature')
    @patch('encryption.views.validate_ecdh_public_key')
    def test_security_logging(self, mock_validate_key, mock_verify_sig, mock_log):
        """Test that security operations are logged"""
        mock_verify_sig.return_value = True
        mock_validate_key.return_value = True
        
        data = self.generate_test_key_bundle_data()
        
        url = reverse('encryption:upload_key_bundle')
        response = self.client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        # Verify logging was called
        mock_log.assert_called()
