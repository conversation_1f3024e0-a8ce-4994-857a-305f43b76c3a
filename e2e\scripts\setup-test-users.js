#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to create test users for WebRTC calling E2E tests
 * This script creates two users and a conversation between them
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:6000/api';

const TEST_USERS = [
  {
    email: '<EMAIL>',
    password: 'password123',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    username: '<EMAIL>'
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    username: '<EMAIL>'
  }
];

async function createUser(userData) {
  try {
    console.log(`Creating user: ${userData.email}`);
    const response = await axios.post(`${API_BASE_URL}/auth/register/`, userData);
    console.log(`✅ User created: ${userData.email}`);
    return response.data;
  } catch (error) {
    if (error.response?.status === 400 && error.response?.data?.email?.[0]?.includes('already exists')) {
      console.log(`ℹ️ User already exists: ${userData.email}`);
      return null;
    }
    console.error(`❌ Failed to create user ${userData.email}:`, error.response?.data || error.message);
    throw error;
  }
}

async function loginUser(email, password) {
  try {
    console.log(`Logging in user: ${email}`);
    const response = await axios.post(`${API_BASE_URL}/auth/login/`, {
      email,
      password
    });
    console.log(`✅ User logged in: ${email}`);
    return response.data.access;
  } catch (error) {
    console.error(`❌ Failed to login user ${email}:`, error.response?.data || error.message);
    throw error;
  }
}

async function createConversation(token1, token2, user1Id, user2Id) {
  try {
    console.log('Creating conversation between users');
    const response = await axios.post(
      `${API_BASE_URL}/messaging/conversations/`,
      {
        type: 'DIRECT',
        participant_ids: [user1Id, user2Id]
      },
      {
        headers: {
          'Authorization': `Bearer ${token1}`,
          'Content-Type': 'application/json'
        }
      }
    );
    console.log('✅ Conversation created');
    return response.data;
  } catch (error) {
    if (error.response?.status === 400 && error.response?.data?.non_field_errors?.[0]?.includes('already exists')) {
      console.log('ℹ️ Conversation already exists');
      return null;
    }
    console.error('❌ Failed to create conversation:', error.response?.data || error.message);
    throw error;
  }
}

async function getUserProfile(token) {
  try {
    const response = await axios.get(`${API_BASE_URL}/auth/user/`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('❌ Failed to get user profile:', error.response?.data || error.message);
    throw error;
  }
}

async function setupTestUsers() {
  console.log('🚀 Setting up test users for WebRTC calling tests');
  console.log('================================================\n');

  try {
    // Create users
    const createdUsers = [];
    for (const userData of TEST_USERS) {
      await createUser(userData);
      createdUsers.push(userData);
    }

    // Login users and get tokens
    const tokens = [];
    const userProfiles = [];
    
    for (const userData of createdUsers) {
      const token = await loginUser(userData.email, userData.password);
      tokens.push(token);
      
      const profile = await getUserProfile(token);
      userProfiles.push(profile);
    }

    // Create conversation between users
    if (userProfiles.length >= 2) {
      await createConversation(
        tokens[0], 
        tokens[1], 
        userProfiles[0].id, 
        userProfiles[1].id
      );
    }

    console.log('\n🎉 Test users setup complete!');
    console.log('==============================');
    console.log('\nTest Users:');
    userProfiles.forEach((profile, index) => {
      console.log(`${index + 1}. ${profile.email} (ID: ${profile.id})`);
    });
    
    console.log('\n📋 Next Steps:');
    console.log('1. Start all services: npm run dev (in root)');
    console.log('2. Run E2E tests: cd e2e && npm test webrtc-calling.spec.ts');
    
  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    process.exit(1);
  }
}

// Check if backend is running
async function checkBackend() {
  try {
    await axios.get(`${API_BASE_URL}/auth/user/`, {
      headers: { 'Authorization': 'Bearer invalid' }
    });
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.error('❌ Backend server is not running!');
      console.error('Please start the backend server first: cd backend && python manage.py runserver 6000');
      process.exit(1);
    }
    // 401 is expected with invalid token, means server is running
  }
}

// Main execution
async function main() {
  await checkBackend();
  await setupTestUsers();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { setupTestUsers, TEST_USERS };
