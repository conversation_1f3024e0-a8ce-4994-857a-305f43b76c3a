// socket-server/src/tests/services/messageService.test.ts
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { PrismaClient } from '@prisma/client'
import { mockDeep, mockReset, DeepMockProxy } from 'vitest-mock-extended'
import { MessageService } from '../../services/messageService'
import { MessageCreate } from '../../schemas'

describe('MessageService', () => {
  let messageService: MessageService
  let prismaMock: DeepMockProxy<PrismaClient>

  const mockUser = {
    id: '123e4567-e89b-12d3-a456-************',
    username: 'testuser',
    email: '<EMAIL>',
    password: 'hashedpassword',
    last_login: null,
    is_superuser: false,
    is_staff: false,
    is_active: true,
    date_joined: new Date('2023-01-01T00:00:00Z'),
    firstName: 'Test',
    lastName: 'User',
    profilePicture: null,
    isVerified: true,
    lastSeen: new Date('2023-01-01T00:00:00Z'),
    createdAt: new Date('2023-01-01T00:00:00Z'),
    updatedAt: new Date('2023-01-01T00:00:00Z'),
  }

  const mockConversation = {
    id: '123e4567-e89b-12d3-a456-************',
    type: 'DIRECT',
    name: null,
    createdAt: new Date('2023-01-01T00:00:00Z'),
    updatedAt: new Date('2023-01-01T00:00:00Z'),
  }

  const mockParticipant = {
    id: '123e4567-e89b-12d3-a456-426614174002',
    conversationId: '123e4567-e89b-12d3-a456-************',
    userId: '123e4567-e89b-12d3-a456-************',
    role: 'MEMBER',
    joinedAt: new Date('2023-01-01T00:00:00Z'),
  }

  const mockMessage = {
    id: '123e4567-e89b-12d3-a456-426614174003',
    conversationId: '123e4567-e89b-12d3-a456-************',
    senderId: '123e4567-e89b-12d3-a456-************',
    content: 'Hello world!',
    messageType: 'TEXT',
    encryptedContent: '',
    message_key_id: null,
    messageNumber: 0,
    previousChainLength: 0,
    senderRatchetKey: null,
    iv: null,
    createdAt: new Date('2023-01-01T00:00:00Z'),
    updatedAt: new Date('2023-01-01T00:00:00Z'),
    sender: mockUser,
  }

  beforeEach(() => {
    prismaMock = mockDeep<PrismaClient>()
    messageService = new MessageService(prismaMock)
  })

  afterEach(() => {
    mockReset(prismaMock)
  })

  describe('createMessage', () => {
    const validMessageData: MessageCreate = {
      content: 'Hello world!',
      conversationId: '123e4567-e89b-12d3-a456-************',
      messageType: 'TEXT',
      tempId: 'temp-123',
    }

    it('should create a message successfully', async () => {
      // Mock conversation access verification
      prismaMock.conversationParticipant.findFirst.mockResolvedValue(mockParticipant)
      
      // Mock message creation
      prismaMock.message.create.mockResolvedValue(mockMessage)
      
      // Mock conversation update
      prismaMock.conversation.update.mockResolvedValue(mockConversation)

      const result = await messageService.createMessage(validMessageData, '123e4567-e89b-12d3-a456-************')

      expect(result).toEqual(mockMessage)
      expect(prismaMock.conversationParticipant.findFirst).toHaveBeenCalledWith({
        where: {
          conversationId: '123e4567-e89b-12d3-a456-************',
          userId: '123e4567-e89b-12d3-a456-************',
        },
      })
      expect(prismaMock.message.create).toHaveBeenCalledWith({
        data: {
          conversationId: '123e4567-e89b-12d3-a456-************',
          senderId: '123e4567-e89b-12d3-a456-************',
          content: 'Hello world!',
          messageType: 'TEXT',
        },
        include: {
          sender: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
              profilePicture: true,
            },
          },
        },
      })
      expect(prismaMock.conversation.update).toHaveBeenCalledWith({
        where: { id: '123e4567-e89b-12d3-a456-************' },
        data: { updatedAt: expect.any(Date) },
      })
    })

    it('should throw error when user has no access to conversation', async () => {
      prismaMock.conversationParticipant.findFirst.mockResolvedValue(null)

      await expect(
        messageService.createMessage(validMessageData, '123e4567-e89b-12d3-a456-************')
      ).rejects.toThrow('Access denied to conversation')

      expect(prismaMock.message.create).not.toHaveBeenCalled()
      expect(prismaMock.conversation.update).not.toHaveBeenCalled()
    })

    it('should validate message data', async () => {
      const invalidMessageData = {
        content: '', // Empty content should fail validation
        conversationId: '123e4567-e89b-12d3-a456-************',
        messageType: 'TEXT',
      } as MessageCreate

      await expect(
        messageService.createMessage(invalidMessageData, '123e4567-e89b-12d3-a456-************')
      ).rejects.toThrow()

      expect(prismaMock.conversationParticipant.findFirst).not.toHaveBeenCalled()
    })

    it('should handle invalid conversation ID format', async () => {
      const invalidMessageData = {
        content: 'Hello',
        conversationId: 'invalid-uuid',
        messageType: 'TEXT',
      } as MessageCreate

      await expect(
        messageService.createMessage(invalidMessageData, '123e4567-e89b-12d3-a456-************')
      ).rejects.toThrow('Invalid UUID')
    })

    it('should handle database errors during message creation', async () => {
      prismaMock.conversationParticipant.findFirst.mockResolvedValue(mockParticipant)
      prismaMock.message.create.mockRejectedValue(new Error('Database error'))

      await expect(
        messageService.createMessage(validMessageData, '123e4567-e89b-12d3-a456-************')
      ).rejects.toThrow('Database error')
    })

    it('should handle database errors during conversation update', async () => {
      prismaMock.conversationParticipant.findFirst.mockResolvedValue(mockParticipant)
      prismaMock.message.create.mockResolvedValue(mockMessage)
      prismaMock.conversation.update.mockRejectedValue(new Error('Update failed'))

      await expect(
        messageService.createMessage(validMessageData, '123e4567-e89b-12d3-a456-************')
      ).rejects.toThrow('Update failed')
    })

    it('should handle different message types', async () => {
      const imageMessageData: MessageCreate = {
        content: 'image.jpg',
        conversationId: '123e4567-e89b-12d3-a456-************',
        messageType: 'IMAGE',
      }

      prismaMock.conversationParticipant.findFirst.mockResolvedValue(mockParticipant)
      prismaMock.message.create.mockResolvedValue({
        ...mockMessage,
        content: 'image.jpg',
        messageType: 'IMAGE',
      })
      prismaMock.conversation.update.mockResolvedValue(mockConversation)

      const result = await messageService.createMessage(imageMessageData, '123e4567-e89b-12d3-a456-************')

      expect(result.messageType).toBe('IMAGE')
      expect(prismaMock.message.create).toHaveBeenCalledWith({
        data: {
          conversationId: '123e4567-e89b-12d3-a456-************',
          senderId: '123e4567-e89b-12d3-a456-************',
          content: 'image.jpg',
          messageType: 'IMAGE',
        },
        include: expect.any(Object),
      })
    })

    it('should handle long message content', async () => {
      const longContent = 'a'.repeat(4000) // Maximum allowed length
      const longMessageData: MessageCreate = {
        content: longContent,
        conversationId: '123e4567-e89b-12d3-a456-************',
        messageType: 'TEXT',
      }

      prismaMock.conversationParticipant.findFirst.mockResolvedValue(mockParticipant)
      prismaMock.message.create.mockResolvedValue({
        ...mockMessage,
        content: longContent,
      })
      prismaMock.conversation.update.mockResolvedValue(mockConversation)

      const result = await messageService.createMessage(longMessageData, '123e4567-e89b-12d3-a456-************')

      expect(result.content).toBe(longContent)
    })

    it('should reject message content that is too long', async () => {
      const tooLongContent = 'a'.repeat(4001) // Exceeds maximum length
      const invalidMessageData: MessageCreate = {
        content: tooLongContent,
        conversationId: '123e4567-e89b-12d3-a456-************',
        messageType: 'TEXT',
      }

      await expect(
        messageService.createMessage(invalidMessageData, '123e4567-e89b-12d3-a456-************')
      ).rejects.toThrow()
    })
  })

  describe('getConversationMessages', () => {
    const mockMessages = [
      {
        ...mockMessage,
        id: 'msg-1',
        content: 'First message',
        createdAt: new Date('2023-01-01T00:00:00Z'),
      },
      {
        ...mockMessage,
        id: 'msg-2',
        content: 'Second message',
        createdAt: new Date('2023-01-01T01:00:00Z'),
      },
    ]

    it('should fetch messages for authorized user', async () => {
      prismaMock.conversationParticipant.findFirst.mockResolvedValue(mockParticipant)
      prismaMock.message.findMany.mockResolvedValue(mockMessages.reverse()) // Database returns in desc order

      const result = await messageService.getConversationMessages('123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************')

      expect(result).toEqual(mockMessages) // Should be reversed to chronological order
      expect(prismaMock.conversationParticipant.findFirst).toHaveBeenCalledWith({
        where: {
          conversationId: '123e4567-e89b-12d3-a456-************',
          userId: '123e4567-e89b-12d3-a456-************',
        },
      })
      expect(prismaMock.message.findMany).toHaveBeenCalledWith({
        where: {
          conversationId: '123e4567-e89b-12d3-a456-************',
        },
        include: {
          sender: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
              profilePicture: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: 0,
        take: 50,
      })
    })

    it('should handle pagination correctly', async () => {
      prismaMock.conversationParticipant.findFirst.mockResolvedValue(mockParticipant)
      prismaMock.message.findMany.mockResolvedValue([mockMessage])

      await messageService.getConversationMessages('123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************', 2, 25)

      expect(prismaMock.message.findMany).toHaveBeenCalledWith({
        where: {
          conversationId: '123e4567-e89b-12d3-a456-************',
        },
        include: expect.any(Object),
        orderBy: {
          createdAt: 'desc',
        },
        skip: 25, // (page 2 - 1) * limit 25
        take: 25,
      })
    })

    it('should throw error when user has no access', async () => {
      prismaMock.conversationParticipant.findFirst.mockResolvedValue(null)

      await expect(
        messageService.getConversationMessages('123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************')
      ).rejects.toThrow('Access denied to conversation')

      expect(prismaMock.message.findMany).not.toHaveBeenCalled()
    })

    it('should handle database errors', async () => {
      prismaMock.conversationParticipant.findFirst.mockResolvedValue(mockParticipant)
      prismaMock.message.findMany.mockRejectedValue(new Error('Database error'))

      await expect(
        messageService.getConversationMessages('123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************')
      ).rejects.toThrow('Database error')
    })

    it('should return empty array when no messages exist', async () => {
      prismaMock.conversationParticipant.findFirst.mockResolvedValue(mockParticipant)
      prismaMock.message.findMany.mockResolvedValue([])

      const result = await messageService.getConversationMessages('123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************')

      expect(result).toEqual([])
    })

    it('should handle custom page size', async () => {
      prismaMock.conversationParticipant.findFirst.mockResolvedValue(mockParticipant)
      prismaMock.message.findMany.mockResolvedValue([mockMessage])

      await messageService.getConversationMessages('123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************', 1, 10)

      expect(prismaMock.message.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          skip: 0,
          take: 10,
        })
      )
    })
  })

  describe('verifyConversationAccess', () => {
    it('should return participant when user has access', async () => {
      prismaMock.conversationParticipant.findFirst.mockResolvedValue(mockParticipant)

      const result = await messageService.verifyConversationAccess('123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************')

      expect(result).toEqual(mockParticipant)
      expect(prismaMock.conversationParticipant.findFirst).toHaveBeenCalledWith({
        where: {
          conversationId: '123e4567-e89b-12d3-a456-************',
          userId: '123e4567-e89b-12d3-a456-************',
        },
      })
    })

    it('should return null when user has no access', async () => {
      prismaMock.conversationParticipant.findFirst.mockResolvedValue(null)

      const result = await messageService.verifyConversationAccess('123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************')

      expect(result).toBeNull()
    })

    it('should return null on database errors', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      prismaMock.conversationParticipant.findFirst.mockRejectedValue(new Error('Database error'))

      const result = await messageService.verifyConversationAccess('123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************')

      expect(result).toBeNull()
      expect(consoleSpy).toHaveBeenCalledWith('Error verifying conversation access:', expect.any(Error))
      
      consoleSpy.mockRestore()
    })
  })

  describe('updateUserStatus', () => {
    it('should update user last seen successfully', async () => {
      prismaMock.user.update.mockResolvedValue({
        ...mockUser,
        lastSeen: new Date(),
      })

      const result = await messageService.updateUserStatus('123e4567-e89b-12d3-a456-************', true)

      expect(result).toBe(true)
      expect(prismaMock.user.update).toHaveBeenCalledWith({
        where: { id: '123e4567-e89b-12d3-a456-************' },
        data: { lastSeen: expect.any(Date) },
      })
    })

    it('should handle database errors gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      prismaMock.user.update.mockRejectedValue(new Error('Database error'))

      const result = await messageService.updateUserStatus('123e4567-e89b-12d3-a456-************', true)

      expect(result).toBe(false)
      expect(consoleSpy).toHaveBeenCalledWith('Error updating user status:', expect.any(Error))

      consoleSpy.mockRestore()
    })

    it('should work with offline status', async () => {
      prismaMock.user.update.mockResolvedValue({
        ...mockUser,
        lastSeen: new Date(),
      })

      const result = await messageService.updateUserStatus('123e4567-e89b-12d3-a456-************', false)

      expect(result).toBe(true)
      expect(prismaMock.user.update).toHaveBeenCalledWith({
        where: { id: '123e4567-e89b-12d3-a456-************' },
        data: { lastSeen: expect.any(Date) },
      })
    })

    it('should handle non-existent user', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      prismaMock.user.update.mockRejectedValue(new Error('User not found'))

      const result = await messageService.updateUserStatus('nonexistent-user', true)

      expect(result).toBe(false)
      expect(consoleSpy).toHaveBeenCalledWith('Error updating user status:', expect.any(Error))

      consoleSpy.mockRestore()
    })
  })
})
