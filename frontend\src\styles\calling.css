/* frontend/src/styles/calling.css */

/* Calling component styles */

/* Incoming call modal animations */
.incoming-call-modal {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Call button hover effects */
.call-btn {
  transition: all 0.2s ease-in-out;
}

.call-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.call-btn:active {
  transform: translateY(0);
}

/* Active call interface animations */
.active-call-interface {
  animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Call controls animations */
.call-controls {
  animation: slideUpControls 0.3s ease-out 0.2s both;
}

@keyframes slideUpControls {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Video container styles */
.video-container {
  position: relative;
  overflow: hidden;
}

.video-container video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background-color: #1f2937;
}

/* Local video overlay */
.local-video-overlay {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 12rem;
  height: 9rem;
  border-radius: 0.5rem;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.local-video-overlay:hover {
  border-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.05);
}

/* Minimized local video */
.local-video-minimized {
  width: 4rem;
  height: 3rem;
  top: 0.5rem;
  right: 0.5rem;
}

/* Connection status indicators */
.connection-indicator {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.connection-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: #10b981;
  animation: pulse 2s infinite;
}

.connection-dot.poor {
  background-color: #ef4444;
}

.connection-dot.fair {
  background-color: #f59e0b;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Call duration display */
.call-duration {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  letter-spacing: 0.05em;
}

/* Ringing animation */
.ringing-animation {
  animation: ring 1s ease-in-out infinite;
}

@keyframes ring {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-10deg);
  }
  75% {
    transform: rotate(10deg);
  }
}

/* Call status overlay */
.call-status-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 1rem 2rem;
  border-radius: 2rem;
  backdrop-filter: blur(10px);
  animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Loading spinner for call connection */
.call-loading-spinner {
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #ffffff;
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .local-video-overlay {
    width: 7.5rem;
    height: 5.625rem;
    top: 0.625rem;
    right: 0.625rem;
  }
  
  .call-controls {
    padding: 1rem;
  }
  
  .call-controls button {
    width: 2.75rem;
    height: 2.75rem;
  }
  
  .incoming-call-modal {
    margin: 1rem;
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .local-video-overlay {
    width: 6rem;
    height: 4.5rem;
  }
  
  .call-controls {
    gap: 0.75rem;
  }
  
  .call-controls button {
    width: 2.5rem;
    height: 2.5rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .call-controls button {
    border: 2px solid currentColor;
  }
  
  .local-video-overlay {
    border-width: 3px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .incoming-call-modal,
  .active-call-interface,
  .call-controls,
  .local-video-overlay,
  .call-status-overlay {
    animation: none;
  }
  
  .connection-dot,
  .ringing-animation,
  .call-loading-spinner {
    animation: none;
  }
  
  .call-btn:hover {
    transform: none;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .video-container video {
    background-color: #111827;
  }
}

/* Focus styles for accessibility */
.call-controls button:focus,
.call-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
