// socket-server/src/schemas/callingSchemas.ts
import { z } from 'zod';

// Base schemas
export const uuidSchema = z.string().uuid();
export const callTypeSchema = z.enum(['audio', 'video']);
export const callStatusSchema = z.enum([
  'initiated', 'ringing', 'answered', 'active', 'ended', 'missed', 'declined', 'failed'
]);

// Call initiation schemas
export const InitiateCallSchema = z.object({
  conversationId: uuidSchema,
  callType: callTypeSchema.default('audio'),
});

export const CallResponseSchema = z.object({
  callId: uuidSchema,
});

// WebRTC signaling schemas
export const WebRTCOfferSchema = z.object({
  callId: uuidSchema,
  offer: z.object({
    type: z.literal('offer'),
    sdp: z.string(),
  }),
});

export const WebRTCAnswerSchema = z.object({
  callId: uuidSchema,
  answer: z.object({
    type: z.literal('answer'),
    sdp: z.string(),
  }),
});

export const WebRTCIceCandidateSchema = z.object({
  callId: uuidSchema,
  candidate: z.object({
    candidate: z.string(),
    sdpMLineIndex: z.number().nullable(),
    sdpMid: z.string().nullable(),
    usernameFragment: z.string().nullable().optional(),
  }),
});

// Media control schemas
export const MediaToggleSchema = z.object({
  callId: uuidSchema,
  mediaType: z.enum(['audio', 'video']),
  enabled: z.boolean(),
});

// Call quality schemas
export const CallQualityReportSchema = z.object({
  callId: uuidSchema,
  metrics: z.object({
    packetLoss: z.number().min(0).max(100).optional(),
    jitter: z.number().min(0).optional(),
    roundTripTime: z.number().min(0).optional(),
    bandwidthUpload: z.number().min(0).optional(),
    bandwidthDownload: z.number().min(0).optional(),
    audioLevel: z.number().min(0).max(1).optional(),
    audioQualityScore: z.number().min(0).max(5).optional(),
    videoResolution: z.string().optional(),
    videoFramerate: z.number().min(0).optional(),
    videoQualityScore: z.number().min(0).max(5).optional(),
  }),
});

// Event data schemas for outgoing events
export const IncomingCallEventSchema = z.object({
  callId: uuidSchema,
  caller: z.object({
    id: uuidSchema,
    username: z.string(),
    firstName: z.string(),
    lastName: z.string(),
    profilePicture: z.string().nullable(),
  }),
  callType: callTypeSchema,
  conversationId: uuidSchema,
  timestamp: z.string(),
});

export const CallAnsweredEventSchema = z.object({
  callId: uuidSchema,
  calleeId: uuidSchema,
  timestamp: z.string(),
});

export const CallDeclinedEventSchema = z.object({
  callId: uuidSchema,
  timestamp: z.string(),
});

export const CallEndedEventSchema = z.object({
  callId: uuidSchema,
  endedBy: uuidSchema,
  reason: z.string().optional(),
  timestamp: z.string(),
});

export const CallRingingEventSchema = z.object({
  callId: uuidSchema,
});

export const CallFailedEventSchema = z.object({
  callId: uuidSchema,
  reason: z.string(),
  timestamp: z.string(),
});

export const ParticipantMediaToggleEventSchema = z.object({
  userId: uuidSchema,
  mediaType: z.enum(['audio', 'video']),
  enabled: z.boolean(),
});

// Validation helper functions
export function validateSocketEvent<T>(schema: z.ZodSchema<T>, data: unknown): T {
  try {
    return schema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new Error(`Validation error: ${error.issues.map((e: z.ZodIssue) => e.message).join(', ')}`);
    }
    throw error;
  }
}

export function createSocketError(message: string, code?: string) {
  return {
    error: true,
    message,
    code: code || 'VALIDATION_ERROR',
    timestamp: new Date().toISOString(),
  };
}

// Type exports
export type InitiateCall = z.infer<typeof InitiateCallSchema>;
export type CallResponse = z.infer<typeof CallResponseSchema>;
export type WebRTCOffer = z.infer<typeof WebRTCOfferSchema>;
export type WebRTCAnswer = z.infer<typeof WebRTCAnswerSchema>;
export type WebRTCIceCandidate = z.infer<typeof WebRTCIceCandidateSchema>;
export type MediaToggle = z.infer<typeof MediaToggleSchema>;
export type CallQualityReport = z.infer<typeof CallQualityReportSchema>;
export type IncomingCallEvent = z.infer<typeof IncomingCallEventSchema>;
export type CallAnsweredEvent = z.infer<typeof CallAnsweredEventSchema>;
export type CallDeclinedEvent = z.infer<typeof CallDeclinedEventSchema>;
export type CallEndedEvent = z.infer<typeof CallEndedEventSchema>;
export type CallRingingEvent = z.infer<typeof CallRingingEventSchema>;
export type CallFailedEvent = z.infer<typeof CallFailedEventSchema>;
export type ParticipantMediaToggleEvent = z.infer<typeof ParticipantMediaToggleEventSchema>;
