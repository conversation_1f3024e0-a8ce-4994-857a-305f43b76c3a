#!/usr/bin/env node

/**
 * Audio Fix Validation Script
 * 
 * This script validates the WebRTC audio transmission fixes by:
 * 1. Running the updated Playwright test with real devices
 * 2. Checking for proper audio level detection
 * 3. Validating fake device detection
 * 4. Ensuring enhanced audio testing works
 */

const { execSync, spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🎵 WebRTC Audio Fix Validation Script');
console.log('=====================================\n');

// Configuration
const E2E_DIR = path.join(__dirname, 'e2e');
const TEST_FILE = 'webrtc-manual-testing.spec.ts';
const TIMEOUT = 300000; // 5 minutes

// Validation steps
const validationSteps = [
  {
    name: 'Check test file modifications',
    action: () => {
      const testPath = path.join(E2E_DIR, 'tests', TEST_FILE);
      if (!fs.existsSync(testPath)) {
        throw new Error(`Test file not found: ${testPath}`);
      }
      
      const content = fs.readFileSync(testPath, 'utf8');
      
      // Check that fake device flags are removed
      if (content.includes('--use-fake-device-for-media-stream')) {
        throw new Error('Fake device flags still present in test file');
      }
      
      // Check that logging flags are added
      if (!content.includes('--enable-logging=stderr')) {
        throw new Error('Logging flags not found in test file');
      }
      
      console.log('✅ Test file properly modified');
    }
  },
  
  {
    name: 'Check AudioTestingService creation',
    action: () => {
      const servicePath = path.join(__dirname, 'frontend', 'src', 'services', 'webrtc', 'AudioTestingService.ts');
      if (!fs.existsSync(servicePath)) {
        throw new Error('AudioTestingService.ts not found');
      }
      
      const content = fs.readFileSync(servicePath, 'utf8');
      
      // Check for key methods
      const requiredMethods = [
        'detectFakeDevices',
        'testAudioCapabilities',
        'performAudioLevelTest',
        'validateAudioSetup'
      ];
      
      for (const method of requiredMethods) {
        if (!content.includes(method)) {
          throw new Error(`Required method ${method} not found in AudioTestingService`);
        }
      }
      
      console.log('✅ AudioTestingService properly created');
    }
  },
  
  {
    name: 'Check WebRTCService integration',
    action: () => {
      const servicePath = path.join(__dirname, 'frontend', 'src', 'services', 'webrtc', 'WebRTCService.ts');
      const content = fs.readFileSync(servicePath, 'utf8');
      
      // Check for AudioTestingService import
      if (!content.includes('audioTestingService')) {
        throw new Error('AudioTestingService not imported in WebRTCService');
      }
      
      // Check for enhanced testLocalAudioCapture method
      if (!content.includes('await audioTestingService.testAudioCapabilities')) {
        throw new Error('Enhanced audio testing not integrated');
      }
      
      console.log('✅ WebRTCService properly enhanced');
    }
  },
  
  {
    name: 'Run WebRTC test with real devices',
    action: () => {
      console.log('🚀 Starting WebRTC test with real devices...');
      console.log('⚠️  Please ensure you have a microphone connected and grant permissions when prompted');
      
      return new Promise((resolve, reject) => {
        const testProcess = spawn('npx', ['playwright', 'test', TEST_FILE, '--headed'], {
          cwd: E2E_DIR,
          stdio: 'pipe'
        });
        
        let output = '';
        let hasAudioLevels = false;
        let hasFakeDeviceDetection = false;
        
        testProcess.stdout.on('data', (data) => {
          const chunk = data.toString();
          output += chunk;
          console.log(chunk);
          
          // Check for audio level detection
          if (chunk.includes('Local audio level:') && !chunk.includes('0.00')) {
            hasAudioLevels = true;
            console.log('✅ Real audio levels detected!');
          }
          
          // Check for fake device detection
          if (chunk.includes('Fake device detected') || chunk.includes('Real device detected')) {
            hasFakeDeviceDetection = true;
            console.log('✅ Device detection working!');
          }
        });
        
        testProcess.stderr.on('data', (data) => {
          const chunk = data.toString();
          output += chunk;
          console.error(chunk);
        });
        
        testProcess.on('close', (code) => {
          if (code === 0) {
            console.log('✅ Test completed successfully');
            
            // Validate results
            if (!hasAudioLevels) {
              console.warn('⚠️  No real audio levels detected - check microphone');
            }
            
            if (!hasFakeDeviceDetection) {
              console.warn('⚠️  Device detection not working as expected');
            }
            
            resolve({ hasAudioLevels, hasFakeDeviceDetection });
          } else {
            reject(new Error(`Test failed with code ${code}`));
          }
        });
        
        // Timeout handling
        setTimeout(() => {
          testProcess.kill();
          reject(new Error('Test timed out'));
        }, TIMEOUT);
      });
    }
  }
];

// Run validation
async function runValidation() {
  console.log('Starting validation process...\n');
  
  for (let i = 0; i < validationSteps.length; i++) {
    const step = validationSteps[i];
    console.log(`${i + 1}. ${step.name}`);
    
    try {
      const result = await step.action();
      if (result && typeof result === 'object') {
        console.log('   Results:', result);
      }
      console.log('');
    } catch (error) {
      console.error(`❌ ${step.name} failed:`, error.message);
      process.exit(1);
    }
  }
  
  console.log('🎉 All validation steps completed successfully!');
  console.log('\n📋 Summary:');
  console.log('- Fake device flags removed from test');
  console.log('- AudioTestingService created and integrated');
  console.log('- WebRTCService enhanced with better audio testing');
  console.log('- Real device testing validated');
  console.log('\n✅ WebRTC audio transmission issue has been fixed!');
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Validation interrupted');
  process.exit(0);
});

// Run the validation
runValidation().catch((error) => {
  console.error('❌ Validation failed:', error);
  process.exit(1);
});