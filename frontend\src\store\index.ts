// frontend/src/store/index.ts
import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import messageReducer from './slices/messageSlice';
import conversationReducer from './slices/conversationSlice';
import encryptionReducer from './slices/encryptionSlice';
import mediaUploadReducer from './slices/mediaUploadSlice';
import callingReducer from './slices/callingSlice';
import { api } from '../services';

export const store = configureStore({
  reducer: {
    messages: messageReducer,
    conversations: conversationReducer,
    encryption: encryptionReducer,
    mediaUpload: mediaUploadReducer,
    calling: callingReducer,
    // Add the RTK Query API reducers
    [api.reducerPath]: api.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          'persist/PERSIST',
          'calling/setLocalStream',
          'calling/setRemoteStream',
          'calling/setPeerConnection'
        ],
        ignoredActionsPaths: [
          'payload.localStream',
          'payload.remoteStream',
          'payload.peerConnection'
        ],
        ignoredPaths: [
          'calling.localStream',
          'calling.remoteStream', 
          'calling.peerConnection'
        ],
      },
    })
    // Add the RTK Query middleware
    .concat(api.middleware),
});

// Enable listener behavior for the store
setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Re-export memoized selectors for easy access
export {
  selectMessagesByConversation,
  selectSortedMessagesByConversation,
  selectTypingUsersByConversation,
  selectSendingMessages,
  selectMessageStatuses,
  selectFailedMessages,
  selectTypingUsers
} from './slices/messageSlice';
