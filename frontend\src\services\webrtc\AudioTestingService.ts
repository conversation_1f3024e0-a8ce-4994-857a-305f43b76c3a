import { MediaService } from './MediaService';

export interface AudioTestResult {
  hasRealAudioDevice: boolean;
  audioLevel: number;
  deviceLabel: string;
  deviceId: string;
  isFakeDevice: boolean;
  testDuration: number;
}

export class AudioTestingService {
  private mediaService: MediaService;

  constructor() {
    this.mediaService = new MediaService();
  }

  /**
   * Comprehensive audio device testing
   * Detects fake devices and measures real audio levels
   */
  async testAudioCapabilities(): Promise<AudioTestResult> {
    console.log('🧪 Starting comprehensive audio device testing...');

    try {
      // Get available audio devices
      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioInputs = devices.filter(device => device.kind === 'audioinput');
      
      console.log(`📊 Found ${audioInputs.length} audio input devices:`);
      audioInputs.forEach((device, index) => {
        console.log(`  ${index + 1}. ${device.label || 'Unknown Device'} (${device.deviceId})`);
      });

      // Test with real audio constraints
      const stream = await this.mediaService.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100,
          channelCount: 1
        },
        video: false
      });

      const audioTracks = stream.getAudioTracks();
      if (audioTracks.length === 0) {
        throw new Error('No audio tracks available');
      }

      const audioTrack = audioTracks[0];
      const settings = audioTrack.getSettings();
      const capabilities = audioTrack.getCapabilities();
      
      console.log('🎤 Audio track settings:', settings);
      console.log('🎤 Audio track capabilities:', capabilities);

      // Detect fake devices by checking device labels and capabilities
      const isFakeDevice = this.detectFakeDevices(audioTrack);
      
      if (isFakeDevice) {
        console.warn('⚠️ FAKE AUDIO DEVICE DETECTED!');
        console.warn('   This will produce silent audio streams.');
        console.warn('   Remove --use-fake-device-for-media-stream flag for real testing.');
      }

      // Perform audio level testing
      const audioTestResult = await this.performAudioLevelTest(stream);
      
      // Clean up
      this.mediaService.stopAllTracks(stream);

      return {
        hasRealAudioDevice: !isFakeDevice,
        audioLevel: audioTestResult.maxLevel,
        deviceLabel: audioTrack.label || 'Unknown Device',
        deviceId: settings.deviceId || 'unknown',
        isFakeDevice,
        testDuration: audioTestResult.duration
      };

    } catch (error) {
      console.error('❌ Audio testing failed:', error);
      throw error;
    }
  }

  /**
   * Detect if the audio device is a fake/mock device
   */
  detectFakeDevices(audioTrack: MediaStreamTrack): boolean {
    const settings = audioTrack.getSettings();
    const label = audioTrack.label.toLowerCase();
    
    // Common indicators of fake devices
    const fakeIndicators = [
      'fake',
      'mock',
      'test',
      'dummy',
      'virtual'
    ];

    // Check label for fake indicators
    const hasFakeLabel = fakeIndicators.some(indicator => label.includes(indicator));
    
    // Check for suspicious settings (fake devices often have default values)
    const hasSuspiciousSettings = (
      settings.sampleRate === 44100 && 
      settings.channelCount === 2 &&
      !settings.deviceId
    );

    return hasFakeLabel || hasSuspiciousSettings;
  }

  /**
   * Perform detailed audio level testing with enhanced detection
   */
  async performAudioLevelTest(stream: MediaStream): Promise<{ maxLevel: number; duration: number }> {
    return new Promise((resolve) => {
      const startTime = Date.now();
      let maxLevel = 0;
      let testCount = 0;
      const maxTests = 10; // Test for 10 seconds

      try {
        const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
        const source = audioContext.createMediaStreamSource(stream);
        const analyser = audioContext.createAnalyser();

        source.connect(analyser);
        analyser.fftSize = 2048; // Higher resolution
        analyser.smoothingTimeConstant = 0.3;

        const bufferLength = analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);
        const frequencyArray = new Uint8Array(bufferLength);

        console.log('🎵 Starting enhanced audio level monitoring...');

        const testInterval = setInterval(() => {
          // Get both time domain and frequency domain data
          analyser.getByteTimeDomainData(dataArray);
          analyser.getByteFrequencyData(frequencyArray);

          // Calculate RMS (Root Mean Square) for more accurate level detection
          let sum = 0;
          for (let i = 0; i < bufferLength; i++) {
            const sample = (dataArray[i] - 128) / 128; // Normalize to -1 to 1
            sum += sample * sample;
          }
          const rms = Math.sqrt(sum / bufferLength);
          const level = rms * 100; // Convert to percentage

          // Calculate frequency domain average
          const freqAverage = frequencyArray.reduce((a, b) => a + b) / bufferLength;

          console.log(`🎤 Audio Test ${testCount + 1}/${maxTests}:`);
          console.log(`   RMS Level: ${level.toFixed(2)}%`);
          console.log(`   Frequency Average: ${freqAverage.toFixed(2)}`);
          console.log(`   Peak Frequency: ${Math.max(...frequencyArray)}`);

          maxLevel = Math.max(maxLevel, level);
          testCount++;

          if (testCount >= maxTests) {
            clearInterval(testInterval);
            audioContext.close();
            
            const duration = Date.now() - startTime;
            console.log(`✅ Audio level testing completed in ${duration}ms`);
            console.log(`📊 Maximum audio level detected: ${maxLevel.toFixed(2)}%`);
            
            if (maxLevel < 0.1) {
              console.warn('⚠️ Very low or no audio detected!');
              console.warn('   This may indicate:');
              console.warn('   - Fake/mock audio device in use');
              console.warn('   - Microphone is muted or not working');
              console.warn('   - No ambient sound in testing environment');
            }

            resolve({ maxLevel, duration });
          }
        }, 1000);

      } catch (error) {
        console.error('❌ Audio level testing failed:', error);
        resolve({ maxLevel: 0, duration: Date.now() - startTime });
      }
    });
  }

  /**
   * Quick audio device validation
   */
  async validateAudioSetup(): Promise<boolean> {
    try {
      const result = await this.testAudioCapabilities();
      
      if (result.isFakeDevice) {
        console.error('❌ Audio validation failed: Fake device detected');
        return false;
      }

      if (result.audioLevel < 0.1) {
        console.warn('⚠️ Audio validation warning: Very low audio levels');
        return false;
      }

      console.log('✅ Audio setup validation passed');
      return true;
    } catch (error) {
      console.error('❌ Audio validation failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const audioTestingService = new AudioTestingService();