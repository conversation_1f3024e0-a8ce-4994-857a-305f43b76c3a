// frontend/src/components/calling/CallControls.tsx
import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  Mic,
  MicOff,
  Video,
  VideoOff,
  PhoneOff,
  Volume2,
  VolumeX,
  Monitor,
  MonitorOff
} from 'lucide-react';
import { useSocket } from '../../contexts/SocketContext';
import { useEndCallMutation } from '../../services/callingApi';
import {
  selectCallControls,
  selectCurrentCall,
  toggleAudio,
  toggleVideo,
  endCall
} from '../../store/slices/callingSlice';
import type { AppDispatch } from '../../store';

interface CallControlsProps {
  isMinimized?: boolean;
}

export const CallControls: React.FC<CallControlsProps> = ({ isMinimized = false }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { toggleCallAudio, toggleCallVideo } = useSocket();
  const [endCallMutation, { isLoading: isEndingCall }] = useEndCallMutation();

  const controls = useSelector(selectCallControls);
  const currentCall = useSelector(selectCurrentCall);

  const handleToggleAudio = async () => {
    try {
      console.log('🎤 Toggling audio...');
      const result = await dispatch(toggleAudio()).unwrap();
      
      // Emit socket event to sync with other participants
      if (currentCall.id) {
        toggleCallAudio(currentCall.id, !result.isAudioMuted);
      }
    } catch (error) {
      console.error('Failed to toggle audio:', error);
    }
  };

  const handleToggleVideo = async () => {
    try {
      console.log('📹 Toggling video...');
      const result = await dispatch(toggleVideo()).unwrap();
      
      // Emit socket event to sync with other participants
      if (currentCall.id) {
        toggleCallVideo(currentCall.id, !result.isVideoMuted);
      }
    } catch (error) {
      console.error('Failed to toggle video:', error);
    }
  };

  const handleToggleSpeaker = () => {
    // TODO: Implement speaker toggle
    console.log('🔊 Speaker toggle not yet implemented');
  };

  const handleScreenShare = () => {
    // TODO: Implement screen sharing
    console.log('🖥️ Screen sharing not yet implemented');
  };

  const handleEndCall = async () => {
    try {
      console.log('📞 Ending call...');

      if (currentCall.id && !isEndingCall) {
        // Use API endpoint to end call (backend will emit socket events)
        console.log('📞 Calling API to end call:', currentCall.id);
        const result = await endCallMutation({ callId: currentCall.id }).unwrap();
        console.log('✅ Call ended successfully via API:', result);

        // Dispatch Redux action to clean up local state
        console.log('🔄 Dispatching local call end action...');
        await dispatch(endCall()).unwrap();
        console.log('✅ Local call state cleaned up');
      } else if (!currentCall.id) {
        console.log('⚠️ No call ID available, cleaning up local state only');
        // If no call ID, just clean up local state
        await dispatch(endCall()).unwrap();
      } else {
        console.log('⚠️ Call end already in progress');
      }
    } catch (error) {
      console.error('❌ Failed to end call:', error);

      // Even if API call fails, try to clean up local state
      try {
        console.log('🔄 Attempting local cleanup after API failure...');
        await dispatch(endCall()).unwrap();
        console.log('✅ Local cleanup completed after API failure');
      } catch (localError) {
        console.error('❌ Local cleanup also failed:', localError);
      }
    }
  };

  // Minimized controls layout
  if (isMinimized) {
    return (
      <div className="flex justify-center space-x-2">
        {/* Audio toggle */}
        <button
          onClick={handleToggleAudio}
          className={`w-8 h-8 rounded-full flex items-center justify-center text-white transition-colors ${
            controls.isAudioMuted 
              ? 'bg-red-500 hover:bg-red-600' 
              : 'bg-gray-600 hover:bg-gray-700'
          }`}
          title={controls.isAudioMuted ? 'Unmute' : 'Mute'}
        >
          {controls.isAudioMuted ? (
            <MicOff className="w-4 h-4" />
          ) : (
            <Mic className="w-4 h-4" />
          )}
        </button>

        {/* Video toggle (only for video calls) */}
        {currentCall.type === 'video' && (
          <button
            onClick={handleToggleVideo}
            className={`w-8 h-8 rounded-full flex items-center justify-center text-white transition-colors ${
              controls.isVideoMuted 
                ? 'bg-red-500 hover:bg-red-600' 
                : 'bg-gray-600 hover:bg-gray-700'
            }`}
            title={controls.isVideoMuted ? 'Turn on camera' : 'Turn off camera'}
          >
            {controls.isVideoMuted ? (
              <VideoOff className="w-4 h-4" />
            ) : (
              <Video className="w-4 h-4" />
            )}
          </button>
        )}

        {/* End call */}
        <button
          data-testid="end-call-btn"
          onClick={handleEndCall}
          disabled={isEndingCall}
          className={`w-8 h-8 rounded-full flex items-center justify-center text-white transition-colors ${
            isEndingCall
              ? 'bg-red-400 cursor-not-allowed'
              : 'bg-red-500 hover:bg-red-600'
          }`}
          title={isEndingCall ? 'Ending call...' : 'End call'}
        >
          <PhoneOff className="w-4 h-4" />
        </button>
      </div>
    );
  }

  // Full controls layout
  return (
    <div className="flex justify-center items-center space-x-4">
      {/* Audio toggle */}
      <button
        onClick={handleToggleAudio}
        className={`w-12 h-12 rounded-full flex items-center justify-center text-white transition-all duration-200 hover:scale-105 active:scale-95 ${
          controls.isAudioMuted 
            ? 'bg-red-500 hover:bg-red-600 shadow-lg shadow-red-500/30' 
            : 'bg-gray-600 hover:bg-gray-700 shadow-lg'
        }`}
        title={controls.isAudioMuted ? 'Unmute' : 'Mute'}
      >
        {controls.isAudioMuted ? (
          <MicOff className="w-6 h-6" />
        ) : (
          <Mic className="w-6 h-6" />
        )}
      </button>

      {/* Video toggle (only for video calls) */}
      {currentCall.type === 'video' && (
        <button
          onClick={handleToggleVideo}
          className={`w-12 h-12 rounded-full flex items-center justify-center text-white transition-all duration-200 hover:scale-105 active:scale-95 ${
            controls.isVideoMuted 
              ? 'bg-red-500 hover:bg-red-600 shadow-lg shadow-red-500/30' 
              : 'bg-gray-600 hover:bg-gray-700 shadow-lg'
          }`}
          title={controls.isVideoMuted ? 'Turn on camera' : 'Turn off camera'}
        >
          {controls.isVideoMuted ? (
            <VideoOff className="w-6 h-6" />
          ) : (
            <Video className="w-6 h-6" />
          )}
        </button>
      )}

      {/* Speaker toggle */}
      <button
        onClick={handleToggleSpeaker}
        className={`w-12 h-12 rounded-full flex items-center justify-center text-white transition-all duration-200 hover:scale-105 active:scale-95 ${
          controls.isSpeakerOn 
            ? 'bg-gray-600 hover:bg-gray-700 shadow-lg' 
            : 'bg-red-500 hover:bg-red-600 shadow-lg shadow-red-500/30'
        }`}
        title={controls.isSpeakerOn ? 'Turn off speaker' : 'Turn on speaker'}
      >
        {controls.isSpeakerOn ? (
          <Volume2 className="w-6 h-6" />
        ) : (
          <VolumeX className="w-6 h-6" />
        )}
      </button>

      {/* Screen share toggle (only for video calls) */}
      {currentCall.type === 'video' && (
        <button
          onClick={handleScreenShare}
          className={`w-12 h-12 rounded-full flex items-center justify-center text-white transition-all duration-200 hover:scale-105 active:scale-95 ${
            controls.isScreenSharing 
              ? 'bg-blue-500 hover:bg-blue-600 shadow-lg shadow-blue-500/30' 
              : 'bg-gray-600 hover:bg-gray-700 shadow-lg'
          }`}
          title={controls.isScreenSharing ? 'Stop screen sharing' : 'Share screen'}
        >
          {controls.isScreenSharing ? (
            <MonitorOff className="w-6 h-6" />
          ) : (
            <Monitor className="w-6 h-6" />
          )}
        </button>
      )}

      {/* End call */}
      <button
        data-testid="end-call-btn"
        onClick={handleEndCall}
        disabled={isEndingCall}
        className={`w-14 h-14 rounded-full flex items-center justify-center text-white transition-all duration-200 shadow-lg ${
          isEndingCall
            ? 'bg-red-400 cursor-not-allowed shadow-red-400/30'
            : 'bg-red-500 hover:bg-red-600 hover:scale-105 active:scale-95 shadow-red-500/30'
        }`}
        title={isEndingCall ? 'Ending call...' : 'End call'}
      >
        <PhoneOff className="w-7 h-7" />
      </button>
    </div>
  );
};

export default CallControls;
