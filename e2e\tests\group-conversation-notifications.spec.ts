import { test, expect, Page, BrowserContext } from '@playwright/test';

test.describe('Group Conversation Notifications', () => {
  
  test('Group creation should notify all members immediately', async ({ browser }) => {
    console.log('🚀 Testing Group Conversation Creation Notifications');

    const aliceContext = await browser.newContext();
    const harryContext = await browser.newContext();

    const alicePage = await aliceContext.newPage();
    const harryPage = await harryContext.newPage();

    try {
      // Step 1: Login both users (using known working users)
      console.log('👤 Logging in <PERSON> and Harry...');
      await loginUser(alicePage, '<EMAIL>', 'testpass123');
      await loginUser(harryPage, '<EMAIL>', 'testpass123');

      // Step 2: Set up notification monitoring for Harry
      console.log('👂 Setting up notification monitoring for Harry...');
      await setupNotificationMonitoring(harryPage);

      // Step 3: Clean up existing conversations
      console.log('🧹 Cleaning up existing conversations...');
      await cleanupConversations(alicePage);
      await cleanupConversations(harryPage);

      // Step 4: Debug localStorage and get user IDs
      console.log('📝 Debugging localStorage and getting user IDs...');

      const aliceUserData = await alicePage.evaluate(() => {
        const user = localStorage.getItem('user');
        const token = localStorage.getItem('token');
        console.log('Alice localStorage user:', user);
        console.log('Alice localStorage token exists:', !!token);
        return { user: user ? JSON.parse(user) : null, hasToken: !!token };
      });

      const harryUserData = await harryPage.evaluate(() => {
        const user = localStorage.getItem('user');
        const token = localStorage.getItem('token');
        console.log('Harry localStorage user:', user);
        console.log('Harry localStorage token exists:', !!token);
        return { user: user ? JSON.parse(user) : null, hasToken: !!token };
      });

      console.log('Alice user data:', aliceUserData);
      console.log('Harry user data:', harryUserData);

      if (!harryUserData.user || !harryUserData.user.id) {
        throw new Error('Harry user ID not found in localStorage');
      }

      const harryUserId = harryUserData.user.id;
      console.log('Harry User ID:', harryUserId);
      
      const groupResult = await alicePage.evaluate(async ({ harryUserId }) => {
        const token = localStorage.getItem('token');
        console.log('Token exists:', !!token);
        console.log('Harry User ID for group:', harryUserId);

        const response = await fetch('/api/messaging/groups/create/', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            name: 'Test Group Chat',
            description: 'Test group for notification testing',
            member_ids: [harryUserId],
            is_public: false,
            max_participants: 50
          })
        });

        const data = await response.json();
        console.log('Group creation response status:', response.status);
        console.log('Group creation response:', data);
        return {
          success: response.ok,
          status: response.status,
          data: data,
          groupId: data.id
        };
      }, { harryUserId });

      console.log('✅ Group creation result:', groupResult);

      if (!groupResult.success) {
        console.log('❌ Group creation failed:', groupResult);
        throw new Error(`Group creation failed with status ${groupResult.status}: ${JSON.stringify(groupResult.data)}`);
      }

      const groupId = groupResult.groupId;
      console.log('✅ Group created with ID:', groupId);

      // Step 5: Verify Harry receives notification
      console.log('⏳ Waiting for Harry to receive group creation notification...');
      await harryPage.waitForTimeout(5000);

      const harryNotificationReceived = await harryPage.evaluate(() => (window as any).conversationNotificationReceived);

      console.log('Harry notification received:', harryNotificationReceived);

      // Check if the group appears in Harry's conversation list after refresh
      console.log('📋 Checking if group appears in conversation lists...');
      await harryPage.reload();

      await harryPage.waitForSelector('[data-testid="conversation-list"]', { timeout: 10000 });

      const harryGroupVisible = await harryPage.locator('text=Test Group Chat').isVisible();

      console.log('Harry can see group:', harryGroupVisible);

      expect(harryGroupVisible).toBe(true);
      console.log('✅ VERIFIED: Group conversation appears in Harry\'s conversation list');

      // If notifications were received, that's even better
      if (harryNotificationReceived) {
        console.log('🎉 BONUS: Harry received real-time notification!');
      } else {
        console.log('⚠️  Real-time notifications not received, but group creation works');
      }

    } finally {
      await aliceContext.close();
      await harryContext.close();
    }
  });

  // Helper functions
  async function loginUser(page: Page, email: string, password: string): Promise<void> {
    await page.goto('http://localhost:5000');
    
    // Wait for login form to be visible
    await page.waitForSelector('form', { timeout: 10000 });
    
    // Fill login form
    await page.fill('[data-testid="email-input"]', email);
    await page.fill('[data-testid="password-input"]', password);
    
    // Submit login
    await page.click('[data-testid="login-button"]');
    
    // Wait for successful login (dashboard header should be visible)
    await page.waitForSelector('[data-testid="dashboard-header"]', { timeout: 15000 });
    
    // Wait for socket connection
    await page.waitForSelector('[data-testid="connection-status"]:has-text("Connected")', { timeout: 15000 });
  }

  async function setupNotificationMonitoring(page: Page): Promise<void> {
    await page.evaluate(() => {
      // Monitor for conversation_created events
      (window as any).conversationNotificationReceived = false;
      (window as any).conversationData = null;
      
      // Hook into the socket context to monitor events
      const originalConsoleLog = console.log;
      console.log = (...args) => {
        if (args[0] && args[0].includes('🔔 [CONVERSATION_CREATED]')) {
          (window as any).conversationNotificationReceived = true;
          if (args[0].includes('Full conversation data:')) {
            (window as any).conversationData = args[1];
          }
        }
        originalConsoleLog.apply(console, args);
      };
    });
  }

  async function cleanupConversations(page: Page): Promise<void> {
    try {
      await page.evaluate(async () => {
        const token = localStorage.getItem('token');
        if (token) {
          const response = await fetch('/api/messaging/conversations/', {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          
          if (response.ok) {
            const data = await response.json();
            for (const conversation of data.results || []) {
              try {
                await fetch(`/api/messaging/conversations/${conversation.id}/`, {
                  method: 'DELETE',
                  headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                  }
                });
              } catch (e) {
                console.log('Could not delete conversation:', conversation.id);
              }
            }
          }
        }
      });
    } catch (error) {
      console.log('Cleanup error (continuing anyway):', error);
    }
  }

  async function getUserId(page: Page): Promise<string> {
    return await page.evaluate(() => {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      console.log('User data from localStorage:', user);
      return user.id;
    });
  }
});
